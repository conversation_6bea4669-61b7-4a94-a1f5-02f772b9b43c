# 🤖 Multi-Agent Code Generation System

A sophisticated multi-agent system that uses three specialized AI agents to analyze, plan, and generate code using VLLM OpenAI-compatible servers.

## 🏗️ Architecture

The system consists of three specialized agents:

- **🔧 Coder Agent**: Analyzes project structures and generates high-quality code
- **🔍 Reviewer Agent**: Reviews code quality, security, and compliance with requirements
- **👔 Manager Agent**: Orchestrates the workflow and synthesizes final results

## ✨ Features

- **Multi-Agent Workflow**: Coordinated workflow using LangGraph for optimal task delegation
- **ReAct Framework**: Agents use reasoning and action patterns for tool usage
- **Memory Management**: Short-term and long-term memory with timestamped logs
- **Token Management**: Intelligent token allocation (< 8196 tokens per agent)
- **VLLM Integration**: Compatible with VLLM OpenAI-compatible servers
- **GUI Interface**: Easy-to-use Streamlit interface
- **CLI Support**: Command-line interface for automation
- **Comprehensive Logging**: Detailed logs with timestamps for review and debugging

## 🚀 Quick Start

### Prerequisites

- Python 3.8 or higher
- VLLM servers running on ports 8001, 8002, and 8003
- Models loaded in VLLM (e.g., Llama 3.1 8B Instruct)

### Installation

1. **Clone and setup:**
   ```bash
   git clone <repository-url>
   cd multi-agent-workflow
   chmod +x setup.sh
   ./setup.sh
   ```

2. **Configure your environment:**
   Edit the `.env` file with your VLLM server details:
   ```env
   CODER_MODEL_URL=http://localhost:8001/v1
   REVIEWER_MODEL_URL=http://localhost:8002/v1
   MANAGER_MODEL_URL=http://localhost:8003/v1

   CODER_MODEL_NAME=llama-3.1-8b-instruct
   REVIEWER_MODEL_NAME=llama-3.1-8b-instruct
   MANAGER_MODEL_NAME=llama-3.1-8b-instruct
   ```

3. **Start your VLLM servers:**
   ```bash
   # Example for starting VLLM servers
   vllm serve llama-3.1-8b-instruct --port 8001 --api-key your-api-key
   vllm serve llama-3.1-8b-instruct --port 8002 --api-key your-api-key
   vllm serve llama-3.1-8b-instruct --port 8003 --api-key your-api-key
   ```

### Usage

#### GUI Mode (Recommended)
```bash
python main.py --mode gui
```
Then open your browser to `http://localhost:8501`

#### CLI Mode
```bash
python main.py --mode cli --request "Create a Python web scraper for e-commerce sites" --folder "/path/to/project"
```

## 🎯 Usage Examples

### Example 1: Web Application
```
Request: "Create a FastAPI web application with user authentication, CRUD operations for a todo list, and SQLite database integration."

Folder: /path/to/existing/project (optional)
```

### Example 2: Data Analysis Tool
```
Request: "Build a Python script that analyzes CSV data, generates visualizations with matplotlib, and exports results to PDF reports."
```

### Example 3: Game Development
```
Request: "Create a simple 2D platformer game using Python and pygame with player movement, collision detection, and score tracking."
```

## 📁 Project Structure

```
multi-agent-workflow/
├── agents/                 # Agent implementations
│   ├── base_agent.py      # Base agent class
│   ├── coder_agent.py     # Code generation agent
│   ├── reviewer_agent.py  # Code review agent
│   └── manager_agent.py   # Workflow manager agent
├── memory/                # Memory management
│   └── memory_manager.py  # Agent memory system
├── tools/                 # Agent tools
│   └── file_tools.py      # File analysis tools
├── utils/                 # Utilities
│   ├── token_manager.py   # Token counting and management
│   └── logger.py          # Logging utilities
├── workflow/              # Workflow orchestration
│   └── agent_workflow.py  # LangGraph workflow
├── gui/                   # User interface
│   └── streamlit_app.py   # Streamlit GUI
├── memory_logs/           # Memory and log storage
├── config.py              # Configuration management
├── main.py                # Main entry point
├── .env                   # Environment variables
├── requirements.txt       # Python dependencies
├── setup.sh               # Setup script
└── README.md              # This file
```

## ⚙️ Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `CODER_MODEL_URL` | VLLM server URL for coder agent | `http://localhost:8001/v1` |
| `REVIEWER_MODEL_URL` | VLLM server URL for reviewer agent | `http://localhost:8002/v1` |
| `MANAGER_MODEL_URL` | VLLM server URL for manager agent | `http://localhost:8003/v1` |
| `TEMPERATURE` | Model temperature | `0.7` |
| `MAX_TOKENS` | Maximum tokens per response | `2048` |
| `MAX_TOKENS_PER_AGENT` | Token limit per agent | `8196` |
| `MEMORY_FOLDER` | Memory logs folder | `./memory_logs` |

### Model Configuration

The system supports any VLLM-compatible model. Recommended models:
- Llama 3.1 8B Instruct
- Code Llama 7B/13B
- Mistral 7B Instruct
- Any OpenAI-compatible model

## 🧠 Memory System

The system maintains comprehensive memory for each agent:

- **Short-term Memory**: Recent interactions (configurable size)
- **Long-term Memory**: Important information and insights
- **Conversation History**: Complete dialogue history
- **Timestamped Logs**: All interactions with timestamps

Memory files are stored in the `memory_logs/` folder:
- `coder_log.txt` - Coder agent logs
- `reviewer_log.txt` - Reviewer agent logs
- `manager_log.txt` - Manager agent logs
- `system_log.txt` - System-wide logs

## 🔧 Workflow Process

1. **Planning**: Manager agent analyzes the request and creates a workflow plan
2. **Analysis**: Coder agent analyzes the project folder (if provided)
3. **Planning**: Coder agent generates a comprehensive project plan
4. **Implementation**: Coder agent generates code based on requirements
5. **Review**: Reviewer agent conducts thorough code review
6. **Validation**: Reviewer agent validates requirements compliance
7. **Synthesis**: Manager agent synthesizes final results
8. **Delivery**: Complete deliverable with documentation

## 🎨 GUI Features

The Streamlit interface provides:

- **Interactive Request Input**: Easy-to-use form for project requests
- **Folder Selection**: Browse and select project folders
- **Real-time Progress**: Live workflow execution updates
- **Detailed Results**: Comprehensive display of all agent outputs
- **Memory Management**: View and manage agent memory logs
- **Workflow History**: Access to previous workflow results
- **Configuration Display**: Current system settings

## 🔍 Troubleshooting

### Common Issues

1. **VLLM Server Connection Failed**
   - Ensure VLLM servers are running on configured ports
   - Check firewall settings
   - Verify API keys if required

2. **Token Limit Exceeded**
   - Reduce `MAX_TOKENS_PER_AGENT` in `.env`
   - Use shorter input requests
   - Clear agent memory if needed

3. **Memory Issues**
   - Clear memory logs: `rm -rf memory_logs/*`
   - Restart the application
   - Check disk space

4. **Import Errors**
   - Ensure virtual environment is activated
   - Reinstall requirements: `pip install -r requirements.txt`

### Debug Mode

Enable detailed logging by setting in `.env`:
```env
LOG_LEVEL=DEBUG
ENABLE_DETAILED_LOGGING=true
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- LangChain and LangGraph for agent framework
- VLLM for high-performance model serving
- Streamlit for the user interface
- The open-source AI community

## 📞 Support

For issues and questions:
1. Check the troubleshooting section
2. Review the memory logs in `memory_logs/`
3. Open an issue on GitHub
4. Check VLLM server logs for model-related issues
