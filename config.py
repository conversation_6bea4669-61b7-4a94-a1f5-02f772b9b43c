"""Configuration management for the multi-agent system."""

import os
from typing import Dict, Any
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Config:
    """Configuration class for the multi-agent system."""
    
    # VLLM Server URLs
    CODER_MODEL_URL = os.getenv("CODER_MODEL_URL", "http://localhost:8001/v1")
    REVIEWER_MODEL_URL = os.getenv("REVIEWER_MODEL_URL", "http://localhost:8002/v1")
    MANAGER_MODEL_URL = os.getenv("MANAGER_MODEL_URL", "http://localhost:8003/v1")
    
    # Model Names
    CODER_MODEL_NAME = os.getenv("CODER_MODEL_NAME", "llama-3.1-8b-instruct")
    REVIEWER_MODEL_NAME = os.getenv("REVIEWER_MODEL_NAME", "llama-3.1-8b-instruct")
    MANAGER_MODEL_NAME = os.getenv("MANAGER_MODEL_NAME", "llama-3.1-8b-instruct")
    
    # Model Parameters
    TEMPERATURE = float(os.getenv("TEMPERATURE", "0.7"))
    MAX_TOKENS = int(os.getenv("MAX_TOKENS", "2048"))
    TOP_P = float(os.getenv("TOP_P", "0.9"))
    FREQUENCY_PENALTY = float(os.getenv("FREQUENCY_PENALTY", "0.0"))
    PRESENCE_PENALTY = float(os.getenv("PRESENCE_PENALTY", "0.0"))
    
    # Token Management
    MAX_TOKENS_PER_AGENT = int(os.getenv("MAX_TOKENS_PER_AGENT", "8196"))
    TOKEN_BUFFER = int(os.getenv("TOKEN_BUFFER", "200"))
    
    # Memory Configuration
    MEMORY_FOLDER = os.getenv("MEMORY_FOLDER", "./memory_logs")
    SHORT_TERM_MEMORY_SIZE = int(os.getenv("SHORT_TERM_MEMORY_SIZE", "10"))
    LONG_TERM_MEMORY_SIZE = int(os.getenv("LONG_TERM_MEMORY_SIZE", "100"))
    
    # API Keys
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "your-api-key-here")
    
    # Logging
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
    ENABLE_DETAILED_LOGGING = os.getenv("ENABLE_DETAILED_LOGGING", "true").lower() == "true"
    
    @classmethod
    def get_model_config(cls, agent_type: str) -> Dict[str, Any]:
        """Get model configuration for a specific agent type."""
        if agent_type == "coder":
            return {
                "base_url": cls.CODER_MODEL_URL,
                "model": cls.CODER_MODEL_NAME,
                "temperature": cls.TEMPERATURE,
                "max_tokens": cls.MAX_TOKENS,
                "top_p": cls.TOP_P,
                "frequency_penalty": cls.FREQUENCY_PENALTY,
                "presence_penalty": cls.PRESENCE_PENALTY,
            }
        elif agent_type == "reviewer":
            return {
                "base_url": cls.REVIEWER_MODEL_URL,
                "model": cls.REVIEWER_MODEL_NAME,
                "temperature": cls.TEMPERATURE,
                "max_tokens": cls.MAX_TOKENS,
                "top_p": cls.TOP_P,
                "frequency_penalty": cls.FREQUENCY_PENALTY,
                "presence_penalty": cls.PRESENCE_PENALTY,
            }
        elif agent_type == "manager":
            return {
                "base_url": cls.MANAGER_MODEL_URL,
                "model": cls.MANAGER_MODEL_NAME,
                "temperature": cls.TEMPERATURE,
                "max_tokens": cls.MAX_TOKENS,
                "top_p": cls.TOP_P,
                "frequency_penalty": cls.FREQUENCY_PENALTY,
                "presence_penalty": cls.PRESENCE_PENALTY,
            }
        else:
            raise ValueError(f"Unknown agent type: {agent_type}")
    
    @classmethod
    def ensure_memory_folder(cls):
        """Ensure the memory folder exists."""
        os.makedirs(cls.MEMORY_FOLDER, exist_ok=True)
