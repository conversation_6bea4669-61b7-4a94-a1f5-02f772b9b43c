"""Streamlit GUI for the multi-agent system."""

import streamlit as st
import os
import json
from datetime import datetime
from typing import Dict, Any

from workflow.agent_workflow import MultiAgentWorkflow
from config import Config

# Page configuration
st.set_page_config(
    page_title="Multi-Agent Code Generation System",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

def initialize_session_state():
    """Initialize session state variables."""
    if "workflow_results" not in st.session_state:
        st.session_state.workflow_results = []
    if "current_workflow" not in st.session_state:
        st.session_state.current_workflow = None
    if "workflow_running" not in st.session_state:
        st.session_state.workflow_running = False

def display_header():
    """Display the application header."""
    st.title("🤖 Multi-Agent Code Generation System")
    st.markdown("""
    This system uses three specialized AI agents to analyze, plan, and generate code:
    - **Coder Agent**: Analyzes projects and generates code
    - **Reviewer Agent**: Reviews code quality and compliance
    - **Manager Agent**: Orchestrates the workflow and synthesizes results
    """)

def display_sidebar():
    """Display the sidebar with configuration and controls."""
    st.sidebar.header("⚙️ Configuration")
    
    # Display current configuration
    with st.sidebar.expander("Current Settings", expanded=False):
        st.write(f"**Coder Model URL:** {Config.CODER_MODEL_URL}")
        st.write(f"**Reviewer Model URL:** {Config.REVIEWER_MODEL_URL}")
        st.write(f"**Manager Model URL:** {Config.MANAGER_MODEL_URL}")
        st.write(f"**Temperature:** {Config.TEMPERATURE}")
        st.write(f"**Max Tokens:** {Config.MAX_TOKENS}")
        st.write(f"**Max Tokens per Agent:** {Config.MAX_TOKENS_PER_AGENT}")
    
    # Memory management
    st.sidebar.header("💾 Memory Management")
    
    if st.sidebar.button("View Memory Logs"):
        display_memory_logs()
    
    if st.sidebar.button("Clear All Memory"):
        clear_all_memory()
    
    # Workflow history
    st.sidebar.header("📋 Workflow History")
    
    if st.session_state.workflow_results:
        selected_workflow = st.sidebar.selectbox(
            "Select Previous Workflow",
            options=range(len(st.session_state.workflow_results)),
            format_func=lambda x: f"Workflow {x+1}: {st.session_state.workflow_results[x]['user_request'][:30]}..."
        )
        
        if st.sidebar.button("Load Selected Workflow"):
            st.session_state.current_workflow = st.session_state.workflow_results[selected_workflow]
            st.rerun()

def display_main_interface():
    """Display the main interface for workflow execution."""
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.header("📝 Project Request")
        
        # User request input
        user_request = st.text_area(
            "Describe what you want to build:",
            height=150,
            placeholder="Example: Create a Python web scraper that extracts product information from e-commerce websites and saves the data to a CSV file. Include error handling and rate limiting."
        )
        
        # Folder path input
        folder_path = st.text_input(
            "Project Folder Path (optional):",
            placeholder="/path/to/your/project/folder",
            help="Specify a local folder to analyze existing code structure"
        )
        
        # Validate folder path
        folder_exists = False
        if folder_path:
            if os.path.exists(folder_path) and os.path.isdir(folder_path):
                folder_exists = True
                st.success(f"✅ Folder found: {folder_path}")
            else:
                st.error(f"❌ Folder not found: {folder_path}")
        
        # Execute workflow button
        if st.button("🚀 Execute Workflow", disabled=st.session_state.workflow_running):
            if user_request.strip():
                execute_workflow(user_request, folder_path if folder_exists else None)
            else:
                st.error("Please provide a project request.")
    
    with col2:
        st.header("🎯 Quick Examples")
        
        examples = [
            "Create a REST API with FastAPI for a todo list application",
            "Build a React component for a user profile dashboard",
            "Develop a Python script for data analysis with pandas",
            "Create a Node.js Express server with authentication",
            "Build a simple game using Python and pygame"
        ]
        
        for i, example in enumerate(examples):
            if st.button(f"Example {i+1}", key=f"example_{i}"):
                st.session_state.example_request = example
                st.rerun()
        
        # Load example into text area
        if hasattr(st.session_state, 'example_request'):
            st.text_area(
                "Selected Example:",
                value=st.session_state.example_request,
                height=100,
                disabled=True
            )

def execute_workflow(user_request: str, folder_path: str = None):
    """Execute the multi-agent workflow."""
    st.session_state.workflow_running = True
    
    # Create workflow instance
    workflow = MultiAgentWorkflow()
    
    # Display progress
    progress_container = st.container()
    with progress_container:
        st.info("🔄 Executing multi-agent workflow...")
        progress_bar = st.progress(0)
        status_text = st.empty()
    
    try:
        # Execute workflow with progress updates
        status_text.text("Initializing workflow...")
        progress_bar.progress(10)
        
        result = workflow.execute_workflow(user_request, folder_path)
        
        progress_bar.progress(100)
        status_text.text("Workflow completed!")
        
        # Store result
        st.session_state.workflow_results.append(result)
        st.session_state.current_workflow = result
        
        # Clear progress display
        progress_container.empty()
        
        # Display success message
        if result["status"] == "completed":
            st.success("✅ Workflow completed successfully!")
        else:
            st.error(f"❌ Workflow failed: {result.get('errors', ['Unknown error'])}")
        
    except Exception as e:
        progress_container.empty()
        st.error(f"❌ Workflow execution failed: {str(e)}")
    
    finally:
        st.session_state.workflow_running = False
        st.rerun()

def display_workflow_results():
    """Display the results of the current workflow."""
    if not st.session_state.current_workflow:
        return
    
    result = st.session_state.current_workflow
    
    st.header("📊 Workflow Results")
    
    # Summary metrics
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Status", result["status"].title())
    
    with col2:
        st.metric("Steps Completed", result["summary"]["total_steps"])
    
    with col3:
        st.metric("Errors", result["summary"]["error_count"])
    
    with col4:
        if result["summary"]["has_review"]:
            quality_score = result["summary"]["overall_quality"]
            st.metric("Quality Score", f"{quality_score:.1f}/10")
        else:
            st.metric("Quality Score", "N/A")
    
    # Detailed results tabs
    tab1, tab2, tab3, tab4, tab5 = st.tabs([
        "📋 Final Result", 
        "🔍 Agent Outputs", 
        "📈 Step Details", 
        "⚠️ Errors", 
        "🔧 Raw Data"
    ])
    
    with tab1:
        display_final_result(result)
    
    with tab2:
        display_agent_outputs(result)
    
    with tab3:
        display_step_details(result)
    
    with tab4:
        display_errors(result)
    
    with tab5:
        display_raw_data(result)

def display_final_result(result: Dict[str, Any]):
    """Display the final synthesized result."""
    if result.get("final_result"):
        st.markdown("### 🎯 Final Deliverable")
        st.markdown(result["final_result"])
    else:
        st.warning("No final result available.")

def display_agent_outputs(result: Dict[str, Any]):
    """Display outputs from individual agents."""
    agent_outputs = result.get("agent_outputs", {})
    
    if "project_plan" in agent_outputs:
        with st.expander("📋 Coder Agent - Project Plan", expanded=True):
            st.markdown(agent_outputs["project_plan"])
    
    if "generated_code" in agent_outputs:
        with st.expander("💻 Coder Agent - Generated Code", expanded=True):
            st.code(agent_outputs["generated_code"], language="python")
    
    if "code_review" in agent_outputs:
        with st.expander("🔍 Reviewer Agent - Code Review", expanded=True):
            review = agent_outputs["code_review"]
            st.markdown(review["review_text"])
            
            col1, col2 = st.columns(2)
            with col1:
                st.metric("Overall Score", f"{review['overall_score']:.1f}/10")
            with col2:
                st.metric("Recommendation", review["recommendation"])
    
    if "requirements_validation" in agent_outputs:
        with st.expander("✅ Reviewer Agent - Requirements Validation", expanded=True):
            validation = agent_outputs["requirements_validation"]
            st.markdown(validation["validation_text"])
            
            col1, col2 = st.columns(2)
            with col1:
                st.metric("Compliance Score", f"{validation['compliance_score']}%")
            with col2:
                st.metric("Rating", validation["compliance_rating"])

def display_step_details(result: Dict[str, Any]):
    """Display details of each workflow step."""
    steps = result.get("results", {})
    
    for step_name, step_result in steps.items():
        with st.expander(f"Step: {step_name.replace('_', ' ').title()}", expanded=False):
            st.json(step_result)

def display_errors(result: Dict[str, Any]):
    """Display any errors that occurred during the workflow."""
    errors = result.get("errors", [])
    
    if errors:
        for i, error in enumerate(errors, 1):
            st.error(f"Error {i}: {error}")
    else:
        st.success("No errors occurred during the workflow.")

def display_raw_data(result: Dict[str, Any]):
    """Display the raw workflow data."""
    st.json(result)

def display_memory_logs():
    """Display memory logs from all agents."""
    st.header("💾 Memory Logs")
    
    memory_folder = Config.MEMORY_FOLDER
    
    if not os.path.exists(memory_folder):
        st.warning("No memory logs found.")
        return
    
    log_files = [f for f in os.listdir(memory_folder) if f.endswith('.txt')]
    
    if not log_files:
        st.warning("No log files found.")
        return
    
    selected_log = st.selectbox("Select Log File", log_files)
    
    if selected_log:
        log_path = os.path.join(memory_folder, selected_log)
        try:
            with open(log_path, 'r', encoding='utf-8') as f:
                log_content = f.read()
            
            st.text_area("Log Content", log_content, height=400)
            
        except Exception as e:
            st.error(f"Error reading log file: {str(e)}")

def clear_all_memory():
    """Clear all memory logs."""
    memory_folder = Config.MEMORY_FOLDER
    
    if os.path.exists(memory_folder):
        try:
            for file in os.listdir(memory_folder):
                file_path = os.path.join(memory_folder, file)
                if os.path.isfile(file_path):
                    os.remove(file_path)
            
            st.success("All memory logs cleared.")
            
        except Exception as e:
            st.error(f"Error clearing memory logs: {str(e)}")
    else:
        st.warning("No memory folder found.")

def main():
    """Main application function."""
    initialize_session_state()
    display_header()
    display_sidebar()
    display_main_interface()
    
    if st.session_state.current_workflow:
        st.divider()
        display_workflow_results()

if __name__ == "__main__":
    main()
