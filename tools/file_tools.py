"""File analysis tools for the multi-agent system."""

import os
import mimetypes
from pathlib import Path
from typing import List, Dict, Any, Optional
from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field

class FileAnalysisInput(BaseModel):
    """Input for file analysis tool."""
    folder_path: str = Field(description="Path to the folder to analyze")
    max_depth: int = Field(default=3, description="Maximum depth to traverse")
    include_content: bool = Field(default=False, description="Whether to include file contents")

class FileAnalysisTool(BaseTool):
    """Tool for analyzing folder structure and contents."""

    name: str = "file_analysis"
    description: str = "Analyze a folder structure and optionally read file contents"
    args_schema: type = FileAnalysisInput

    def _run(self, folder_path: str, max_depth: int = 3, include_content: bool = False) -> Dict[str, Any]:
        """Analyze the folder structure."""
        try:
            if not os.path.exists(folder_path):
                return {"error": f"Folder path does not exist: {folder_path}"}

            if not os.path.isdir(folder_path):
                return {"error": f"Path is not a directory: {folder_path}"}

            analysis = {
                "folder_path": folder_path,
                "structure": self._analyze_structure(folder_path, max_depth),
                "summary": self._generate_summary(folder_path, max_depth),
                "file_types": self._analyze_file_types(folder_path, max_depth)
            }

            if include_content:
                analysis["file_contents"] = self._read_file_contents(folder_path, max_depth)

            return analysis

        except Exception as e:
            return {"error": f"Error analyzing folder: {str(e)}"}

    def _analyze_structure(self, folder_path: str, max_depth: int, current_depth: int = 0) -> Dict[str, Any]:
        """Recursively analyze folder structure."""
        if current_depth >= max_depth:
            return {"truncated": True}

        structure = {
            "type": "directory",
            "name": os.path.basename(folder_path),
            "path": folder_path,
            "children": []
        }

        try:
            items = sorted(os.listdir(folder_path))
            for item in items:
                item_path = os.path.join(folder_path, item)

                if os.path.isdir(item_path):
                    # Skip hidden directories and common build/cache directories
                    if not item.startswith('.') and item not in ['node_modules', '__pycache__', '.git', 'dist', 'build']:
                        child_structure = self._analyze_structure(item_path, max_depth, current_depth + 1)
                        structure["children"].append(child_structure)
                else:
                    # Add file information
                    file_info = {
                        "type": "file",
                        "name": item,
                        "path": item_path,
                        "size": os.path.getsize(item_path),
                        "extension": Path(item).suffix.lower(),
                        "mime_type": mimetypes.guess_type(item_path)[0]
                    }
                    structure["children"].append(file_info)

        except PermissionError:
            structure["error"] = "Permission denied"
        except Exception as e:
            structure["error"] = str(e)

        return structure

    def _generate_summary(self, folder_path: str, max_depth: int) -> Dict[str, Any]:
        """Generate a summary of the folder contents."""
        summary = {
            "total_files": 0,
            "total_directories": 0,
            "total_size": 0,
            "file_extensions": {},
            "programming_languages": set(),
            "project_type": "unknown"
        }

        for root, dirs, files in os.walk(folder_path):
            # Limit depth
            depth = root[len(folder_path):].count(os.sep)
            if depth >= max_depth:
                dirs[:] = []  # Don't recurse further
                continue

            # Skip hidden and build directories
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['node_modules', '__pycache__', 'dist', 'build']]

            summary["total_directories"] += len(dirs)

            for file in files:
                if not file.startswith('.'):
                    file_path = os.path.join(root, file)
                    try:
                        file_size = os.path.getsize(file_path)
                        summary["total_files"] += 1
                        summary["total_size"] += file_size

                        # Track file extensions
                        ext = Path(file).suffix.lower()
                        if ext:
                            summary["file_extensions"][ext] = summary["file_extensions"].get(ext, 0) + 1

                            # Identify programming languages
                            if ext in ['.py', '.pyw']:
                                summary["programming_languages"].add('Python')
                            elif ext in ['.js', '.jsx', '.ts', '.tsx']:
                                summary["programming_languages"].add('JavaScript/TypeScript')
                            elif ext in ['.java']:
                                summary["programming_languages"].add('Java')
                            elif ext in ['.cpp', '.cc', '.cxx', '.c']:
                                summary["programming_languages"].add('C/C++')
                            elif ext in ['.cs']:
                                summary["programming_languages"].add('C#')
                            elif ext in ['.go']:
                                summary["programming_languages"].add('Go')
                            elif ext in ['.rs']:
                                summary["programming_languages"].add('Rust')
                            elif ext in ['.php']:
                                summary["programming_languages"].add('PHP')
                            elif ext in ['.rb']:
                                summary["programming_languages"].add('Ruby')
                            elif ext in ['.html', '.htm']:
                                summary["programming_languages"].add('HTML')
                            elif ext in ['.css', '.scss', '.sass']:
                                summary["programming_languages"].add('CSS')
                            elif ext in ['.sql']:
                                summary["programming_languages"].add('SQL')
                            elif ext in ['.sh', '.bash']:
                                summary["programming_languages"].add('Shell')
                            elif ext in ['.yaml', '.yml']:
                                summary["programming_languages"].add('YAML')
                            elif ext in ['.json']:
                                summary["programming_languages"].add('JSON')
                            elif ext in ['.xml']:
                                summary["programming_languages"].add('XML')

                    except (OSError, IOError):
                        continue

        # Convert set to list for JSON serialization
        summary["programming_languages"] = list(summary["programming_languages"])

        # Determine project type based on files present
        summary["project_type"] = self._determine_project_type(folder_path)

        return summary

    def _determine_project_type(self, folder_path: str) -> str:
        """Determine the type of project based on key files."""
        key_files = os.listdir(folder_path)

        if 'package.json' in key_files:
            return 'Node.js/JavaScript'
        elif 'requirements.txt' in key_files or 'pyproject.toml' in key_files or 'setup.py' in key_files:
            return 'Python'
        elif 'Cargo.toml' in key_files:
            return 'Rust'
        elif 'go.mod' in key_files:
            return 'Go'
        elif 'pom.xml' in key_files or 'build.gradle' in key_files:
            return 'Java'
        elif 'composer.json' in key_files:
            return 'PHP'
        elif 'Gemfile' in key_files:
            return 'Ruby'
        elif any(f.endswith('.csproj') or f.endswith('.sln') for f in key_files):
            return 'C#/.NET'
        elif 'CMakeLists.txt' in key_files or 'Makefile' in key_files:
            return 'C/C++'
        elif 'index.html' in key_files:
            return 'Web/HTML'
        else:
            return 'Unknown'

    def _analyze_file_types(self, folder_path: str, max_depth: int) -> Dict[str, List[str]]:
        """Analyze file types and categorize them."""
        file_types = {
            "source_code": [],
            "configuration": [],
            "documentation": [],
            "data": [],
            "media": [],
            "other": []
        }

        for root, dirs, files in os.walk(folder_path):
            depth = root[len(folder_path):].count(os.sep)
            if depth >= max_depth:
                dirs[:] = []
                continue

            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['node_modules', '__pycache__', 'dist', 'build']]

            for file in files:
                if not file.startswith('.'):
                    ext = Path(file).suffix.lower()
                    relative_path = os.path.relpath(os.path.join(root, file), folder_path)

                    if ext in ['.py', '.js', '.jsx', '.ts', '.tsx', '.java', '.cpp', '.c', '.cs', '.go', '.rs', '.php', '.rb']:
                        file_types["source_code"].append(relative_path)
                    elif ext in ['.json', '.yaml', '.yml', '.toml', '.ini', '.cfg', '.conf', '.xml']:
                        file_types["configuration"].append(relative_path)
                    elif ext in ['.md', '.txt', '.rst', '.doc', '.docx', '.pdf']:
                        file_types["documentation"].append(relative_path)
                    elif ext in ['.csv', '.json', '.xml', '.sql', '.db', '.sqlite']:
                        file_types["data"].append(relative_path)
                    elif ext in ['.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico', '.mp4', '.mp3', '.wav']:
                        file_types["media"].append(relative_path)
                    else:
                        file_types["other"].append(relative_path)

        return file_types

    def _read_file_contents(self, folder_path: str, max_depth: int, max_file_size: int = 10000) -> Dict[str, str]:
        """Read contents of text files (limited by size)."""
        file_contents = {}

        for root, dirs, files in os.walk(folder_path):
            depth = root[len(folder_path):].count(os.sep)
            if depth >= max_depth:
                dirs[:] = []
                continue

            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['node_modules', '__pycache__', 'dist', 'build']]

            for file in files:
                if not file.startswith('.'):
                    file_path = os.path.join(root, file)
                    relative_path = os.path.relpath(file_path, folder_path)

                    try:
                        # Only read text files under size limit
                        if os.path.getsize(file_path) <= max_file_size:
                            mime_type, _ = mimetypes.guess_type(file_path)
                            if mime_type and mime_type.startswith('text'):
                                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                                    file_contents[relative_path] = f.read()
                    except (OSError, IOError, UnicodeDecodeError):
                        file_contents[relative_path] = "[Error reading file]"

        return file_contents
