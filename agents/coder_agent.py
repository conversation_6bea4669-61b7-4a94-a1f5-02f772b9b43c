"""Coder agent specialized in code generation and analysis."""

from typing import List, Dict, Any
from langchain_core.tools import BaseTool

from agents.base_agent import BaseAgent
from tools.file_tools import FileAnalysisTool

class CoderAgent(BaseAgent):
    """Agent specialized in code generation and analysis."""

    def __init__(self):
        """Initialize the coder agent."""
        tools = [
            FileAnalysisTool()
        ]
        super().__init__("coder", "coder", tools)

    def get_agent_description(self) -> str:
        """Get the description of this agent for prompts."""
        return """a skilled software developer and code architect. You excel at:
- Analyzing project requirements and folder structures
- Writing clean, efficient, and well-documented code
- Following best practices and design patterns
- Creating comprehensive project plans and architectures
- Understanding various programming languages and frameworks
- Generating code that is maintainable and scalable"""

    def get_system_prompt(self) -> str:
        """Get the system prompt for this agent."""
        return """You are a Coder Agent. Generate high-quality code and project plans.

Key tasks:
- Analyze project requirements
- Create code implementations
- Follow best practices
- Provide clear documentation

Be concise and focus on practical solutions."""

    def analyze_project_folder(self, folder_path: str) -> Dict[str, Any]:
        """Analyze a project folder and generate insights."""
        try:
            self.logger.log_workflow_step("analyze_project", "started", f"Analyzing {folder_path}")

            # Use the file analysis tool
            file_tool = FileAnalysisTool()
            analysis_result = file_tool._run(folder_path, max_depth=3, include_content=True)

            if "error" in analysis_result:
                self.logger.log_error(f"Project analysis failed: {analysis_result['error']}")
                return analysis_result

            # Store analysis in memory
            self.memory.add_long_term_memory(
                f"Project analysis for {folder_path}: {analysis_result['summary']}",
                "project_analysis",
                {"folder_path": folder_path, "analysis": analysis_result}
            )

            self.logger.log_workflow_step("analyze_project", "completed", f"Found {analysis_result['summary']['total_files']} files")

            return analysis_result

        except Exception as e:
            error_msg = f"Error analyzing project folder: {str(e)}"
            self.logger.log_error(error_msg, e)
            return {"error": error_msg}

    def generate_project_plan(self, requirements: str, folder_analysis: Dict[str, Any] = None) -> str:
        """Generate a comprehensive project plan based on requirements."""
        try:
            self.logger.log_workflow_step("generate_plan", "started", "Creating project plan")

            # Build context from folder analysis if provided
            context = ""
            if folder_analysis and "error" not in folder_analysis:
                summary = folder_analysis.get("summary", {})
                context = f"""
Existing Project Analysis:
- Project Type: {summary.get('project_type', 'Unknown')}
- Programming Languages: {', '.join(summary.get('programming_languages', []))}
- Total Files: {summary.get('total_files', 0)}
- File Extensions: {summary.get('file_extensions', {})}
"""

            # Create concise prompt for project planning
            planning_prompt = f"""Create a project plan for:

Requirements: {requirements}

Context: {context}

Include:
1. Overview and goals
2. Tech stack recommendations
3. File structure
4. Implementation steps
5. Key considerations

Be specific and actionable."""

            # Generate the plan
            plan = self.process_message(planning_prompt)

            # Store the plan in memory
            self.memory.add_long_term_memory(
                f"Project plan: {plan[:200]}...",
                "project_plan",
                {"requirements": requirements, "full_plan": plan}
            )

            self.logger.log_workflow_step("generate_plan", "completed", f"Generated plan with {len(plan)} characters")

            return plan

        except Exception as e:
            error_msg = f"Error generating project plan: {str(e)}"
            self.logger.log_error(error_msg, e)
            return f"Failed to generate project plan: {error_msg}"

    def generate_code(self, specification: str, context: Dict[str, Any] = None) -> str:
        """Generate code based on specifications."""
        try:
            self.logger.log_workflow_step("generate_code", "started", "Generating code")

            # Build context from previous analysis and plans
            memory_context = self._build_memory_context()

            # Create concise prompt for code generation
            code_prompt = f"""Generate code for:

{specification}

Context: {memory_context}

Requirements:
- Follow best practices
- Include error handling
- Add comments
- Provide usage examples

Be practical and complete."""

            # Generate the code
            code_response = self.process_message(code_prompt)

            # Store the generated code in memory
            self.memory.add_long_term_memory(
                f"Generated code for: {specification[:100]}...",
                "code_generation",
                {"specification": specification, "code": code_response}
            )

            self.logger.log_workflow_step("generate_code", "completed", f"Generated {len(code_response)} characters of code")

            return code_response

        except Exception as e:
            error_msg = f"Error generating code: {str(e)}"
            self.logger.log_error(error_msg, e)
            return f"Failed to generate code: {error_msg}"

    def review_and_improve_code(self, code: str, requirements: str = "") -> str:
        """Review existing code and suggest improvements."""
        try:
            self.logger.log_workflow_step("review_code", "started", "Reviewing code")

            review_prompt = f"""
Please review the following code and provide detailed feedback and improvements:

Code to review:
{code}

Original requirements (if available):
{requirements}

Please provide:
1. **Code Quality Assessment**
   - Strengths of the current implementation
   - Areas that need improvement
   - Adherence to best practices

2. **Specific Issues Found**
   - Bugs or potential errors
   - Performance concerns
   - Security vulnerabilities
   - Maintainability issues

3. **Improved Code**
   - Refactored version with improvements
   - Explanation of changes made
   - Alternative approaches considered

4. **Recommendations**
   - Additional features or enhancements
   - Testing strategies
   - Documentation improvements
"""

            # Generate the review
            review_response = self.process_message(review_prompt)

            # Store the review in memory
            self.memory.add_long_term_memory(
                f"Code review completed: {review_response[:200]}...",
                "code_review",
                {"original_code": code[:500], "review": review_response}
            )

            self.logger.log_workflow_step("review_code", "completed", f"Generated review with {len(review_response)} characters")

            return review_response

        except Exception as e:
            error_msg = f"Error reviewing code: {str(e)}"
            self.logger.log_error(error_msg, e)
            return f"Failed to review code: {error_msg}"
