"""Reviewer agent specialized in code review and validation."""

from typing import List, Dict, Any
from langchain_core.tools import BaseTool

from agents.base_agent import BaseAgent

class ReviewerAgent(BaseAgent):
    """Agent specialized in code review and validation."""

    def __init__(self):
        """Initialize the reviewer agent."""
        super().__init__("reviewer", "reviewer", [])

    def get_agent_description(self) -> str:
        """Get the description of this agent for prompts."""
        return """a meticulous code reviewer and quality assurance expert. You excel at:
- Conducting thorough code reviews and audits
- Identifying bugs, security vulnerabilities, and performance issues
- Ensuring code follows best practices and standards
- Validating that implementations meet requirements
- Providing constructive feedback and improvement suggestions
- Assessing code maintainability and scalability"""

    def get_system_prompt(self) -> str:
        """Get the system prompt for this agent."""
        return """You are a Reviewer Agent. Review code quality and compliance.

Key tasks:
- Identify bugs and issues
- Check requirements compliance
- Assess code quality
- Provide improvement suggestions

Be specific and constructive in feedback."""

    def review_code(self, code: str, requirements: str = "", context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Conduct a comprehensive code review."""
        try:
            self.logger.log_workflow_step("code_review", "started", "Starting comprehensive code review")

            # Build review prompt
            review_prompt = f"""Review this code:

CODE: {code}

REQUIREMENTS: {requirements}

CONTEXT: {context if context else "None"}

Assess:
1. Correctness (Rate 1-10)
2. Security (Rate 1-10)
3. Performance (Rate 1-10)
4. Quality (Rate 1-10)
5. Requirements compliance (Rate 1-10)

Overall score: 1-10
Recommendation: APPROVE/APPROVE_WITH_CHANGES/REJECT

List specific issues and improvements."""

            # Generate the review
            review_response = self.process_message(review_prompt)

            # Parse the review to extract scores and recommendation
            review_data = self._parse_review_response(review_response)

            # Store the review in memory
            self.memory.add_long_term_memory(
                f"Code review completed - Overall score: {review_data.get('overall_score', 'N/A')}",
                "code_review",
                {
                    "code_snippet": code[:200] + "..." if len(code) > 200 else code,
                    "requirements": requirements,
                    "review_data": review_data
                }
            )

            self.logger.log_workflow_step("code_review", "completed", f"Review completed with score {review_data.get('overall_score', 'N/A')}")

            return {
                "review_text": review_response,
                "review_data": review_data,
                "recommendation": review_data.get("recommendation", "UNKNOWN"),
                "overall_score": review_data.get("overall_score", 0)
            }

        except Exception as e:
            error_msg = f"Error conducting code review: {str(e)}"
            self.logger.log_error(error_msg, e)
            return {
                "error": error_msg,
                "review_text": f"Failed to complete review: {error_msg}",
                "recommendation": "ERROR",
                "overall_score": 0
            }

    def validate_requirements_compliance(self, code: str, requirements: str) -> Dict[str, Any]:
        """Validate that code meets the specified requirements."""
        try:
            self.logger.log_workflow_step("requirements_validation", "started", "Validating requirements compliance")

            validation_prompt = f"""
Please validate whether the following code implementation meets the specified requirements:

REQUIREMENTS:
{requirements}

CODE IMPLEMENTATION:
{code}

Please provide a detailed validation report:

1. **REQUIREMENT ANALYSIS**
   - Break down the requirements into specific, testable criteria
   - Identify functional and non-functional requirements

2. **COMPLIANCE CHECK**
   - For each requirement, check if it's implemented correctly
   - Provide specific evidence from the code
   - Mark as: FULLY_MET / PARTIALLY_MET / NOT_MET / NOT_APPLICABLE

3. **MISSING FUNCTIONALITY**
   - List any requirements that are not implemented
   - Explain what's missing and how it should be addressed

4. **EXTRA FUNCTIONALITY**
   - Identify any functionality not specified in requirements
   - Assess whether it's beneficial or unnecessary

5. **COMPLIANCE SCORE**
   - Calculate percentage of requirements met (0-100%)
   - Provide overall compliance rating: EXCELLENT / GOOD / FAIR / POOR

6. **RECOMMENDATIONS**
   - Suggest specific changes to improve compliance
   - Prioritize the most critical missing requirements

Please be specific and provide evidence for your assessments.
"""

            # Generate the validation
            validation_response = self.process_message(validation_prompt)

            # Parse validation results
            validation_data = self._parse_validation_response(validation_response)

            # Store validation in memory
            self.memory.add_long_term_memory(
                f"Requirements validation - Compliance: {validation_data.get('compliance_score', 'N/A')}%",
                "requirements_validation",
                {
                    "requirements": requirements[:200] + "..." if len(requirements) > 200 else requirements,
                    "validation_data": validation_data
                }
            )

            self.logger.log_workflow_step("requirements_validation", "completed", f"Compliance score: {validation_data.get('compliance_score', 'N/A')}%")

            return {
                "validation_text": validation_response,
                "validation_data": validation_data,
                "compliance_score": validation_data.get("compliance_score", 0),
                "compliance_rating": validation_data.get("compliance_rating", "UNKNOWN")
            }

        except Exception as e:
            error_msg = f"Error validating requirements: {str(e)}"
            self.logger.log_error(error_msg, e)
            return {
                "error": error_msg,
                "validation_text": f"Failed to validate requirements: {error_msg}",
                "compliance_score": 0,
                "compliance_rating": "ERROR"
            }

    def _parse_review_response(self, review_text: str) -> Dict[str, Any]:
        """Parse the review response to extract structured data."""
        review_data = {}

        try:
            # Extract scores (simple pattern matching)
            import re

            # Look for rating patterns
            score_patterns = [
                r"Overall quality score:\s*(\d+)",
                r"Rate:\s*(\d+)",
                r"Score:\s*(\d+)"
            ]

            scores = []
            for pattern in score_patterns:
                matches = re.findall(pattern, review_text, re.IGNORECASE)
                scores.extend([int(match) for match in matches])

            if scores:
                review_data["overall_score"] = sum(scores) / len(scores)

            # Extract recommendation
            if "APPROVE_WITH_CHANGES" in review_text.upper():
                review_data["recommendation"] = "APPROVE_WITH_CHANGES"
            elif "APPROVE" in review_text.upper():
                review_data["recommendation"] = "APPROVE"
            elif "REJECT" in review_text.upper():
                review_data["recommendation"] = "REJECT"
            else:
                review_data["recommendation"] = "UNKNOWN"

        except Exception as e:
            self.logger.log_error(f"Error parsing review response: {str(e)}", e)

        return review_data

    def _parse_validation_response(self, validation_text: str) -> Dict[str, Any]:
        """Parse the validation response to extract structured data."""
        validation_data = {}

        try:
            import re

            # Extract compliance score
            score_match = re.search(r"(\d+)%", validation_text)
            if score_match:
                validation_data["compliance_score"] = int(score_match.group(1))

            # Extract compliance rating
            rating_patterns = ["EXCELLENT", "GOOD", "FAIR", "POOR"]
            for rating in rating_patterns:
                if rating in validation_text.upper():
                    validation_data["compliance_rating"] = rating
                    break

        except Exception as e:
            self.logger.log_error(f"Error parsing validation response: {str(e)}", e)

        return validation_data
