"""Reviewer agent specialized in code review and validation."""

from typing import List, Dict, Any
from langchain_core.tools import BaseTool

from agents.base_agent import BaseAgent

class ReviewerAgent(BaseAgent):
    """Agent specialized in code review and validation."""

    def __init__(self):
        """Initialize the reviewer agent."""
        super().__init__("reviewer", "reviewer", [])

    def get_agent_description(self) -> str:
        """Get the description of this agent for prompts."""
        return """a meticulous code reviewer and quality assurance expert. You excel at:
- Conducting thorough code reviews and audits
- Identifying bugs, security vulnerabilities, and performance issues
- Ensuring code follows best practices and standards
- Validating that implementations meet requirements
- Providing constructive feedback and improvement suggestions
- Assessing code maintainability and scalability"""

    def get_system_prompt(self) -> str:
        """Get the system prompt for this agent."""
        return """You are a Reviewer Agent, an expert in code review and quality assurance with extensive experience across multiple programming languages and frameworks.

Your primary responsibilities:
1. Conduct thorough code reviews and quality assessments
2. Identify bugs, security vulnerabilities, and performance issues
3. Validate that code implementations meet specified requirements
4. Ensure adherence to coding standards and best practices
5. Provide constructive feedback and actionable improvement suggestions
6. Assess code maintainability, scalability, and documentation quality

When reviewing code, focus on:
- **Correctness**: Does the code work as intended and handle edge cases?
- **Security**: Are there any security vulnerabilities or unsafe practices?
- **Performance**: Are there any performance bottlenecks or inefficiencies?
- **Maintainability**: Is the code readable, well-structured, and easy to modify?
- **Best Practices**: Does the code follow language-specific conventions and patterns?
- **Requirements Compliance**: Does the implementation meet the specified requirements?
- **Testing**: Is the code testable and are appropriate tests included?
- **Documentation**: Is the code properly documented and commented?

Always provide specific, actionable feedback with examples and suggestions for improvement. Be thorough but constructive in your reviews."""

    def review_code(self, code: str, requirements: str = "", context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Conduct a comprehensive code review."""
        try:
            self.logger.log_workflow_step("code_review", "started", "Starting comprehensive code review")

            # Build review prompt
            review_prompt = f"""
Please conduct a comprehensive code review of the following implementation:

CODE TO REVIEW:
{code}

ORIGINAL REQUIREMENTS:
{requirements}

ADDITIONAL CONTEXT:
{context if context else "No additional context provided"}

Please provide a detailed review covering the following aspects:

1. **CORRECTNESS ASSESSMENT**
   - Does the code implement the requirements correctly?
   - Are there any logical errors or bugs?
   - How well does it handle edge cases and error conditions?
   - Rate: 1-10 (10 = perfect implementation)

2. **SECURITY ANALYSIS**
   - Are there any security vulnerabilities?
   - Does it follow secure coding practices?
   - Are inputs properly validated and sanitized?
   - Rate: 1-10 (10 = highly secure)

3. **PERFORMANCE EVALUATION**
   - Are there any performance bottlenecks?
   - Is the algorithm/approach efficient?
   - Are resources used appropriately?
   - Rate: 1-10 (10 = optimal performance)

4. **CODE QUALITY**
   - Is the code readable and well-structured?
   - Are naming conventions appropriate?
   - Is the code properly organized and modular?
   - Rate: 1-10 (10 = excellent quality)

5. **BEST PRACTICES COMPLIANCE**
   - Does it follow language-specific conventions?
   - Are appropriate design patterns used?
   - Is error handling implemented properly?
   - Rate: 1-10 (10 = exemplary practices)

6. **MAINTAINABILITY**
   - How easy would it be to modify or extend this code?
   - Is it well-documented and commented?
   - Are dependencies managed appropriately?
   - Rate: 1-10 (10 = highly maintainable)

7. **REQUIREMENTS COMPLIANCE**
   - Does the implementation fully meet the specified requirements?
   - Are there any missing features or functionality?
   - Are there any unnecessary additions?
   - Rate: 1-10 (10 = perfect compliance)

8. **SPECIFIC ISSUES FOUND**
   - List any bugs, vulnerabilities, or problems identified
   - Provide specific line numbers or code sections where applicable
   - Suggest specific fixes for each issue

9. **IMPROVEMENT RECOMMENDATIONS**
   - Suggest specific improvements with code examples
   - Recommend alternative approaches if applicable
   - Identify opportunities for optimization

10. **OVERALL ASSESSMENT**
    - Overall quality score: 1-10
    - Recommendation: APPROVE / APPROVE_WITH_CHANGES / REJECT
    - Summary of key strengths and weaknesses

Please be thorough, specific, and constructive in your review.
"""

            # Generate the review
            review_response = self.process_message(review_prompt)

            # Parse the review to extract scores and recommendation
            review_data = self._parse_review_response(review_response)

            # Store the review in memory
            self.memory.add_long_term_memory(
                f"Code review completed - Overall score: {review_data.get('overall_score', 'N/A')}",
                "code_review",
                {
                    "code_snippet": code[:200] + "..." if len(code) > 200 else code,
                    "requirements": requirements,
                    "review_data": review_data
                }
            )

            self.logger.log_workflow_step("code_review", "completed", f"Review completed with score {review_data.get('overall_score', 'N/A')}")

            return {
                "review_text": review_response,
                "review_data": review_data,
                "recommendation": review_data.get("recommendation", "UNKNOWN"),
                "overall_score": review_data.get("overall_score", 0)
            }

        except Exception as e:
            error_msg = f"Error conducting code review: {str(e)}"
            self.logger.log_error(error_msg, e)
            return {
                "error": error_msg,
                "review_text": f"Failed to complete review: {error_msg}",
                "recommendation": "ERROR",
                "overall_score": 0
            }

    def validate_requirements_compliance(self, code: str, requirements: str) -> Dict[str, Any]:
        """Validate that code meets the specified requirements."""
        try:
            self.logger.log_workflow_step("requirements_validation", "started", "Validating requirements compliance")

            validation_prompt = f"""
Please validate whether the following code implementation meets the specified requirements:

REQUIREMENTS:
{requirements}

CODE IMPLEMENTATION:
{code}

Please provide a detailed validation report:

1. **REQUIREMENT ANALYSIS**
   - Break down the requirements into specific, testable criteria
   - Identify functional and non-functional requirements

2. **COMPLIANCE CHECK**
   - For each requirement, check if it's implemented correctly
   - Provide specific evidence from the code
   - Mark as: FULLY_MET / PARTIALLY_MET / NOT_MET / NOT_APPLICABLE

3. **MISSING FUNCTIONALITY**
   - List any requirements that are not implemented
   - Explain what's missing and how it should be addressed

4. **EXTRA FUNCTIONALITY**
   - Identify any functionality not specified in requirements
   - Assess whether it's beneficial or unnecessary

5. **COMPLIANCE SCORE**
   - Calculate percentage of requirements met (0-100%)
   - Provide overall compliance rating: EXCELLENT / GOOD / FAIR / POOR

6. **RECOMMENDATIONS**
   - Suggest specific changes to improve compliance
   - Prioritize the most critical missing requirements

Please be specific and provide evidence for your assessments.
"""

            # Generate the validation
            validation_response = self.process_message(validation_prompt)

            # Parse validation results
            validation_data = self._parse_validation_response(validation_response)

            # Store validation in memory
            self.memory.add_long_term_memory(
                f"Requirements validation - Compliance: {validation_data.get('compliance_score', 'N/A')}%",
                "requirements_validation",
                {
                    "requirements": requirements[:200] + "..." if len(requirements) > 200 else requirements,
                    "validation_data": validation_data
                }
            )

            self.logger.log_workflow_step("requirements_validation", "completed", f"Compliance score: {validation_data.get('compliance_score', 'N/A')}%")

            return {
                "validation_text": validation_response,
                "validation_data": validation_data,
                "compliance_score": validation_data.get("compliance_score", 0),
                "compliance_rating": validation_data.get("compliance_rating", "UNKNOWN")
            }

        except Exception as e:
            error_msg = f"Error validating requirements: {str(e)}"
            self.logger.log_error(error_msg, e)
            return {
                "error": error_msg,
                "validation_text": f"Failed to validate requirements: {error_msg}",
                "compliance_score": 0,
                "compliance_rating": "ERROR"
            }

    def _parse_review_response(self, review_text: str) -> Dict[str, Any]:
        """Parse the review response to extract structured data."""
        review_data = {}

        try:
            # Extract scores (simple pattern matching)
            import re

            # Look for rating patterns
            score_patterns = [
                r"Overall quality score:\s*(\d+)",
                r"Rate:\s*(\d+)",
                r"Score:\s*(\d+)"
            ]

            scores = []
            for pattern in score_patterns:
                matches = re.findall(pattern, review_text, re.IGNORECASE)
                scores.extend([int(match) for match in matches])

            if scores:
                review_data["overall_score"] = sum(scores) / len(scores)

            # Extract recommendation
            if "APPROVE_WITH_CHANGES" in review_text.upper():
                review_data["recommendation"] = "APPROVE_WITH_CHANGES"
            elif "APPROVE" in review_text.upper():
                review_data["recommendation"] = "APPROVE"
            elif "REJECT" in review_text.upper():
                review_data["recommendation"] = "REJECT"
            else:
                review_data["recommendation"] = "UNKNOWN"

        except Exception as e:
            self.logger.log_error(f"Error parsing review response: {str(e)}", e)

        return review_data

    def _parse_validation_response(self, validation_text: str) -> Dict[str, Any]:
        """Parse the validation response to extract structured data."""
        validation_data = {}

        try:
            import re

            # Extract compliance score
            score_match = re.search(r"(\d+)%", validation_text)
            if score_match:
                validation_data["compliance_score"] = int(score_match.group(1))

            # Extract compliance rating
            rating_patterns = ["EXCELLENT", "GOOD", "FAIR", "POOR"]
            for rating in rating_patterns:
                if rating in validation_text.upper():
                    validation_data["compliance_rating"] = rating
                    break

        except Exception as e:
            self.logger.log_error(f"Error parsing validation response: {str(e)}", e)

        return validation_data
