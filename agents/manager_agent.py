"""Manager agent for orchestrating the multi-agent workflow."""

from typing import List, Dict, Any, Optional
from langchain_core.tools import BaseTool

from agents.base_agent import BaseAgent

class ManagerAgent(BaseAgent):
    """Agent responsible for managing and coordinating the workflow."""

    def __init__(self):
        """Initialize the manager agent."""
        super().__init__("manager", "manager", [])

    def get_agent_description(self) -> str:
        """Get the description of this agent for prompts."""
        return """a project manager and workflow coordinator. You excel at:
- Orchestrating complex multi-agent workflows
- Making strategic decisions about task delegation
- Evaluating and synthesizing outputs from multiple agents
- Ensuring project requirements are met comprehensively
- Managing token allocation and resource constraints
- Providing final validation and quality assurance"""

    def get_system_prompt(self) -> str:
        """Get the system prompt for this agent."""
        return """You are a Manager Agent. Coordinate workflow and synthesize results.

Key tasks:
- Plan workflows
- Coordinate agents
- Synthesize final results
- Ensure quality outcomes

Be organized and focus on clear deliverables."""

    def plan_workflow(self, user_request: str, folder_path: str = None) -> Dict[str, Any]:
        """Plan the workflow for handling a user request."""
        try:
            self.logger.log_workflow_step("workflow_planning", "started", f"Planning workflow for: {user_request[:100]}...")

            planning_prompt = f"""Plan workflow for:

REQUEST: {user_request}

FOLDER: {folder_path if folder_path else "None"}

Create plan with:
1. Goal analysis
2. Task breakdown
3. Agent assignments
4. Success criteria

Be concise and actionable."""

            # Generate the workflow plan
            plan_response = self.process_message(planning_prompt)

            # Parse and structure the plan
            workflow_plan = self._parse_workflow_plan(plan_response)

            # Store the plan in memory
            self.memory.add_long_term_memory(
                f"Workflow plan created for: {user_request[:100]}...",
                "workflow_plan",
                {
                    "user_request": user_request,
                    "folder_path": folder_path,
                    "plan": workflow_plan
                }
            )

            self.logger.log_workflow_step("workflow_planning", "completed", f"Created plan with {len(workflow_plan.get('tasks', []))} tasks")

            return {
                "plan_text": plan_response,
                "structured_plan": workflow_plan,
                "estimated_complexity": workflow_plan.get("complexity", "medium"),
                "estimated_duration": workflow_plan.get("duration", "unknown")
            }

        except Exception as e:
            error_msg = f"Error planning workflow: {str(e)}"
            self.logger.log_error(error_msg, e)
            return {
                "error": error_msg,
                "plan_text": f"Failed to create workflow plan: {error_msg}",
                "structured_plan": {},
                "estimated_complexity": "unknown",
                "estimated_duration": "unknown"
            }

    def evaluate_agent_output(self, agent_name: str, output: str, task_description: str) -> Dict[str, Any]:
        """Evaluate the output from an agent."""
        try:
            self.logger.log_workflow_step("output_evaluation", "started", f"Evaluating output from {agent_name}")

            evaluation_prompt = f"""
Evaluate the following output from the {agent_name} agent:

TASK DESCRIPTION:
{task_description}

AGENT OUTPUT:
{output}

Please provide a comprehensive evaluation:

1. **QUALITY ASSESSMENT**
   - How well does the output address the task?
   - Is the output complete and comprehensive?
   - Rate quality: 1-10 (10 = excellent)

2. **COMPLETENESS CHECK**
   - Are all aspects of the task covered?
   - Is any important information missing?
   - Rate completeness: 1-10 (10 = fully complete)

3. **ACCURACY VALIDATION**
   - Is the information provided accurate?
   - Are there any errors or inconsistencies?
   - Rate accuracy: 1-10 (10 = perfectly accurate)

4. **USEFULNESS EVALUATION**
   - How useful is this output for the overall project?
   - Does it provide actionable insights or solutions?
   - Rate usefulness: 1-10 (10 = extremely useful)

5. **NEXT STEPS RECOMMENDATION**
   - Should this output be accepted as-is?
   - Does it need revision or improvement?
   - What should happen next in the workflow?
   - Recommendation: ACCEPT / REVISE / REJECT / REQUEST_MORE_INFO

6. **SPECIFIC FEEDBACK**
   - Highlight the strongest aspects of the output
   - Identify areas that need improvement
   - Suggest specific enhancements if needed

Please provide a balanced, constructive evaluation.
"""

            # Generate the evaluation
            evaluation_response = self.process_message(evaluation_prompt)

            # Parse evaluation results
            evaluation_data = self._parse_evaluation_response(evaluation_response)

            # Store evaluation in memory
            self.memory.add_long_term_memory(
                f"Evaluated {agent_name} output - Score: {evaluation_data.get('overall_score', 'N/A')}",
                "output_evaluation",
                {
                    "agent_name": agent_name,
                    "task": task_description[:100] + "..." if len(task_description) > 100 else task_description,
                    "evaluation_data": evaluation_data
                }
            )

            self.logger.log_workflow_step("output_evaluation", "completed", f"Score: {evaluation_data.get('overall_score', 'N/A')}")

            return {
                "evaluation_text": evaluation_response,
                "evaluation_data": evaluation_data,
                "recommendation": evaluation_data.get("recommendation", "UNKNOWN"),
                "overall_score": evaluation_data.get("overall_score", 0)
            }

        except Exception as e:
            error_msg = f"Error evaluating agent output: {str(e)}"
            self.logger.log_error(error_msg, e)
            return {
                "error": error_msg,
                "evaluation_text": f"Failed to evaluate output: {error_msg}",
                "recommendation": "ERROR",
                "overall_score": 0
            }

    def synthesize_final_result(self, workflow_results: Dict[str, Any], user_request: str) -> Dict[str, Any]:
        """Synthesize the final result from all agent outputs."""
        try:
            self.logger.log_workflow_step("result_synthesis", "started", "Synthesizing final result")

            synthesis_prompt = f"""
Synthesize a comprehensive final result based on the following workflow outputs:

ORIGINAL USER REQUEST:
{user_request}

WORKFLOW RESULTS:
{workflow_results}

Please create a comprehensive final deliverable that includes:

1. **EXECUTIVE SUMMARY**
   - Brief overview of what was accomplished
   - Key outcomes and deliverables
   - Overall success assessment

2. **DETAILED RESULTS**
   - Complete project plan (if generated)
   - Code implementation (if generated)
   - Review findings and recommendations
   - Any additional insights or suggestions

3. **QUALITY ASSESSMENT**
   - Overall quality score for the deliverables
   - Confidence level in the results
   - Areas of strength and potential concerns

4. **IMPLEMENTATION GUIDANCE**
   - Next steps for the user
   - Setup and deployment instructions
   - Testing recommendations
   - Maintenance considerations

5. **LESSONS LEARNED**
   - Key insights from the development process
   - Recommendations for future improvements
   - Alternative approaches considered

6. **FINAL VALIDATION**
   - Confirmation that user requirements were met
   - Assessment of deliverable completeness
   - Overall recommendation: READY_FOR_USE / NEEDS_REFINEMENT / REQUIRES_MAJOR_CHANGES

Please provide a well-structured, actionable final result.
"""

            # Generate the synthesis
            synthesis_response = self.process_message(synthesis_prompt)

            # Parse synthesis results
            synthesis_data = self._parse_synthesis_response(synthesis_response)

            # Store final result in memory
            self.memory.add_long_term_memory(
                f"Final result synthesized - Status: {synthesis_data.get('final_status', 'N/A')}",
                "final_result",
                {
                    "user_request": user_request[:200] + "..." if len(user_request) > 200 else user_request,
                    "synthesis_data": synthesis_data
                }
            )

            self.logger.log_workflow_step("result_synthesis", "completed", f"Status: {synthesis_data.get('final_status', 'N/A')}")

            return {
                "final_result": synthesis_response,
                "synthesis_data": synthesis_data,
                "final_status": synthesis_data.get("final_status", "UNKNOWN"),
                "quality_score": synthesis_data.get("quality_score", 0)
            }

        except Exception as e:
            error_msg = f"Error synthesizing final result: {str(e)}"
            self.logger.log_error(error_msg, e)
            return {
                "error": error_msg,
                "final_result": f"Failed to synthesize final result: {error_msg}",
                "final_status": "ERROR",
                "quality_score": 0
            }

    def _parse_workflow_plan(self, plan_text: str) -> Dict[str, Any]:
        """Parse workflow plan text into structured data."""
        # Simple parsing - in a real implementation, you might use more sophisticated NLP
        plan_data = {
            "tasks": [],
            "complexity": "medium",
            "duration": "unknown"
        }

        try:
            # Extract basic information using simple pattern matching
            if "high complexity" in plan_text.lower() or "complex" in plan_text.lower():
                plan_data["complexity"] = "high"
            elif "low complexity" in plan_text.lower() or "simple" in plan_text.lower():
                plan_data["complexity"] = "low"

            # Count task-like items
            import re
            task_patterns = re.findall(r'\d+\.\s*([^\n]+)', plan_text)
            plan_data["tasks"] = task_patterns[:10]  # Limit to 10 tasks

        except Exception as e:
            self.logger.log_error(f"Error parsing workflow plan: {str(e)}", e)

        return plan_data

    def _parse_evaluation_response(self, evaluation_text: str) -> Dict[str, Any]:
        """Parse evaluation response into structured data."""
        evaluation_data = {}

        try:
            import re

            # Extract scores
            scores = re.findall(r'Rate.*?(\d+)', evaluation_text, re.IGNORECASE)
            if scores:
                numeric_scores = [int(score) for score in scores]
                evaluation_data["overall_score"] = sum(numeric_scores) / len(numeric_scores)

            # Extract recommendation
            recommendations = ["ACCEPT", "REVISE", "REJECT", "REQUEST_MORE_INFO"]
            for rec in recommendations:
                if rec in evaluation_text.upper():
                    evaluation_data["recommendation"] = rec
                    break

        except Exception as e:
            self.logger.log_error(f"Error parsing evaluation response: {str(e)}", e)

        return evaluation_data

    def _parse_synthesis_response(self, synthesis_text: str) -> Dict[str, Any]:
        """Parse synthesis response into structured data."""
        synthesis_data = {}

        try:
            import re

            # Extract quality score
            score_match = re.search(r'quality score.*?(\d+)', synthesis_text, re.IGNORECASE)
            if score_match:
                synthesis_data["quality_score"] = int(score_match.group(1))

            # Extract final status
            statuses = ["READY_FOR_USE", "NEEDS_REFINEMENT", "REQUIRES_MAJOR_CHANGES"]
            for status in statuses:
                if status in synthesis_text.upper():
                    synthesis_data["final_status"] = status
                    break

        except Exception as e:
            self.logger.log_error(f"Error parsing synthesis response: {str(e)}", e)

        return synthesis_data
