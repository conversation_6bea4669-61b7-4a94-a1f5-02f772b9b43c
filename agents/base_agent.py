"""Base agent class with memory and LLM integration."""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from langchain_community.llms.vllm import VLLMOpenAI
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage
from langchain.agents import create_react_agent, AgentExecutor
from langchain_core.prompts import PromptTemplate
from langchain_core.tools import BaseTool

from config import Config
from memory.memory_manager import Agent<PERSON><PERSON>ory
from utils.token_manager import TokenManager
from utils.logger import AgentLogger

class BaseAgent(ABC):
    """Base class for all agents in the system."""

    def __init__(self, agent_name: str, agent_type: str, tools: List[BaseTool] = None):
        """Initialize the base agent."""
        self.agent_name = agent_name
        self.agent_type = agent_type
        self.tools = tools or []

        # Initialize components
        self.memory = AgentMemory(agent_name)
        self.token_manager = TokenManager()
        self.logger = AgentLogger(agent_name)

        # Initialize LLM
        model_config = Config.get_model_config(agent_type)
        self.llm = VLLMOpenAI(
            openai_api_key=Config.OPENAI_API_KEY,
            openai_api_base=model_config["base_url"],
            model_name=model_config["model"],
            temperature=model_config["temperature"],
            max_tokens=model_config["max_tokens"],
            top_p=model_config["top_p"],
            frequency_penalty=model_config["frequency_penalty"],
            presence_penalty=model_config["presence_penalty"],
        )

        # Create agent executor if tools are provided
        self.agent_executor = None
        if self.tools:
            self._create_agent_executor()

        self.logger.log_interaction("INIT", f"Agent {agent_name} initialized with {len(self.tools)} tools")

    def _create_agent_executor(self):
        """Create the ReAct agent executor."""
        try:
            # Create the ReAct prompt template
            react_prompt = PromptTemplate.from_template(self._get_react_prompt_template())

            # Create the agent
            agent = create_react_agent(
                llm=self.llm,
                tools=self.tools,
                prompt=react_prompt
            )

            # Create agent executor
            self.agent_executor = AgentExecutor(
                agent=agent,
                tools=self.tools,
                verbose=True,
                max_iterations=5,
                handle_parsing_errors=True
            )

        except Exception as e:
            self.logger.log_error(f"Failed to create agent executor: {str(e)}", e)

    def _get_react_prompt_template(self) -> str:
        """Get the ReAct prompt template for this agent."""
        return """You are {agent_description}

You have access to the following tools:
{tools}

Use the following format:

Question: the input question you must answer
Thought: you should always think about what to do
Action: the action to take, should be one of [{tool_names}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now know the final answer
Final Answer: the final answer to the original input question

Begin!

Previous conversation and context:
{memory_context}

Question: {input}
Thought: {agent_scratchpad}"""

    @abstractmethod
    def get_agent_description(self) -> str:
        """Get the description of this agent for prompts."""
        pass

    @abstractmethod
    def get_system_prompt(self) -> str:
        """Get the system prompt for this agent."""
        pass

    def process_message(self, message: str, context: Dict[str, Any] = None) -> str:
        """Process a message and return a response."""
        try:
            # Add message to conversation history
            self.memory.add_conversation_turn("user", message)

            # Get memory context
            memory_context = self._build_memory_context()

            # Check token limits
            full_input = f"{memory_context}\n\nUser: {message}"
            if not self.token_manager.is_within_limit(full_input):
                # Truncate memory context if needed
                memory_context = self._truncate_memory_context(message)
                full_input = f"{memory_context}\n\nUser: {message}"

            # Process with agent executor if available, otherwise use direct LLM
            if self.agent_executor:
                response = self._process_with_tools(message, memory_context, context)
            else:
                response = self._process_direct(message, memory_context, context)

            # Add response to conversation history and memory
            self.memory.add_conversation_turn("assistant", response)
            self.memory.add_short_term_memory(
                f"User: {message}\nAssistant: {response}",
                "conversation",
                context
            )

            self.logger.log_interaction("RESPONSE", response[:200] + "..." if len(response) > 200 else response)

            return response

        except Exception as e:
            error_msg = f"Error processing message: {str(e)}"
            self.logger.log_error(error_msg, e)
            return f"I encountered an error while processing your request: {error_msg}"

    def _process_with_tools(self, message: str, memory_context: str, context: Dict[str, Any] = None) -> str:
        """Process message using agent executor with tools."""
        try:
            # Prepare input for agent
            agent_input = {
                "input": message,
                "agent_description": self.get_agent_description(),
                "memory_context": memory_context
            }

            # Run the agent
            result = self.agent_executor.invoke(agent_input)

            # Log tool usage
            if "intermediate_steps" in result:
                for step in result["intermediate_steps"]:
                    if len(step) >= 2:
                        action, observation = step[0], step[1]
                        self.logger.log_tool_usage(
                            action.tool if hasattr(action, 'tool') else "unknown",
                            action.tool_input if hasattr(action, 'tool_input') else "",
                            observation
                        )

            return result.get("output", "I couldn't generate a proper response.")

        except Exception as e:
            self.logger.log_error(f"Error in tool processing: {str(e)}", e)
            return self._process_direct(message, memory_context, context)

    def _process_direct(self, message: str, memory_context: str, context: Dict[str, Any] = None) -> str:
        """Process message using direct LLM call."""
        try:
            # Build the full prompt
            system_prompt = self.get_system_prompt()
            full_prompt = f"{system_prompt}\n\n{memory_context}\n\nUser: {message}\n\nAssistant:"

            # Generate response
            response = self.llm.invoke(full_prompt)

            return response.strip()

        except Exception as e:
            self.logger.log_error(f"Error in direct processing: {str(e)}", e)
            return "I'm sorry, I encountered an error while processing your request."

    def _build_memory_context(self) -> str:
        """Build memory context for the prompt."""
        context_parts = []

        # Add relevant long-term memories
        long_term_memories = self.memory.get_relevant_long_term_memories("", 3)
        if long_term_memories:
            context_parts.append("Relevant past experiences:")
            for memory in long_term_memories:
                context_parts.append(f"- {memory.content}")

        # Add recent short-term memories
        recent_memories = self.memory.get_recent_memories(5)
        if recent_memories:
            context_parts.append("\nRecent interactions:")
            for memory in recent_memories:
                context_parts.append(f"- {memory.content}")

        # Add recent conversation
        recent_conversation = self.memory.get_conversation_context(5)
        if recent_conversation:
            context_parts.append("\nRecent conversation:")
            for turn in recent_conversation[-3:]:  # Last 3 turns
                role = turn["role"].title()
                content = turn["content"][:100] + "..." if len(turn["content"]) > 100 else turn["content"]
                context_parts.append(f"{role}: {content}")

        return "\n".join(context_parts)

    def _truncate_memory_context(self, current_message: str) -> str:
        """Truncate memory context to fit within token limits."""
        system_prompt = self.get_system_prompt()
        base_prompt = f"{system_prompt}\n\nUser: {current_message}\n\nAssistant:"
        base_tokens = self.token_manager.count_tokens(base_prompt)

        available_tokens = Config.MAX_TOKENS_PER_AGENT - base_tokens - Config.TOKEN_BUFFER

        # Start with minimal context and add what fits
        context_parts = []
        current_tokens = 0

        # Try to add recent conversation first
        recent_conversation = self.memory.get_conversation_context(3)
        if recent_conversation:
            conv_text = "Recent conversation:\n"
            for turn in recent_conversation:
                turn_text = f"{turn['role'].title()}: {turn['content']}\n"
                turn_tokens = self.token_manager.count_tokens(turn_text)

                if current_tokens + turn_tokens <= available_tokens:
                    conv_text += turn_text
                    current_tokens += turn_tokens
                else:
                    break

            if current_tokens > 0:
                context_parts.append(conv_text.strip())

        return "\n".join(context_parts)

    def consolidate_memory(self):
        """Consolidate agent memory."""
        self.memory.consolidate_memories()
        self.logger.log_interaction("MEMORY", "Memory consolidation completed")

    def get_memory_summary(self) -> Dict[str, Any]:
        """Get a summary of the agent's memory."""
        return self.memory.get_memory_summary()
