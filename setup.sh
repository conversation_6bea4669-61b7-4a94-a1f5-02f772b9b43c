#!/bin/bash

# Multi-Agent System Setup Script

echo "🤖 Multi-Agent Code Generation System Setup"
echo "============================================"

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3.8 or higher."
    exit 1
fi

# Check Python version
python_version=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
echo "✅ Python version: $python_version"

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "🔄 Activating virtual environment..."
source venv/bin/activate

# Upgrade pip
echo "⬆️ Upgrading pip..."
pip install --upgrade pip

# Install requirements
echo "📥 Installing requirements..."
pip install -r requirements.txt

# Create memory folder
echo "📁 Creating memory folder..."
mkdir -p memory_logs

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "⚠️ .env file not found. Using default configuration."
    echo "Please update the .env file with your VLLM server URLs and model names."
else
    echo "✅ .env file found."
fi

echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "Next steps:"
echo "1. Update the .env file with your VLLM server configurations"
echo "2. Ensure your VLLM servers are running on ports 8001, 8002, and 8003"
echo "3. Run the system:"
echo "   - GUI mode: python main.py --mode gui"
echo "   - CLI mode: python main.py --mode cli --request 'Your request here'"
echo ""
echo "For more information, see the README.md file."
