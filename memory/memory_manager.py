"""Memory management system for agents."""

import json
import os
from datetime import datetime
from typing import List, Dict, Any, Optional
from collections import deque
from config import Config
from utils.logger import AgentLogger

class MemoryEntry:
    """Represents a single memory entry."""
    
    def __init__(self, content: str, memory_type: str, metadata: Dict[str, Any] = None):
        self.content = content
        self.memory_type = memory_type
        self.timestamp = datetime.now().isoformat()
        self.metadata = metadata or {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert memory entry to dictionary."""
        return {
            "content": self.content,
            "memory_type": self.memory_type,
            "timestamp": self.timestamp,
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MemoryEntry':
        """Create memory entry from dictionary."""
        entry = cls(data["content"], data["memory_type"], data.get("metadata", {}))
        entry.timestamp = data["timestamp"]
        return entry

class AgentMemory:
    """Memory system for individual agents."""
    
    def __init__(self, agent_name: str):
        self.agent_name = agent_name
        self.logger = AgentLogger(agent_name)
        
        # Short-term memory (recent interactions)
        self.short_term_memory: deque = deque(maxlen=Config.SHORT_TERM_MEMORY_SIZE)
        
        # Long-term memory (important information)
        self.long_term_memory: List[MemoryEntry] = []
        
        # Conversation history
        self.conversation_history: List[Dict[str, str]] = []
        
        # Memory file paths
        Config.ensure_memory_folder()
        self.memory_file = os.path.join(Config.MEMORY_FOLDER, f"{agent_name}_memory.json")
        self.conversation_file = os.path.join(Config.MEMORY_FOLDER, f"{agent_name}_conversation.json")
        
        # Load existing memory
        self._load_memory()
    
    def add_short_term_memory(self, content: str, memory_type: str = "interaction", metadata: Dict[str, Any] = None):
        """Add entry to short-term memory."""
        entry = MemoryEntry(content, memory_type, metadata)
        self.short_term_memory.append(entry)
        self.logger.log_memory_update("short_term", "add", content)
        self._save_memory()
    
    def add_long_term_memory(self, content: str, memory_type: str = "important", metadata: Dict[str, Any] = None):
        """Add entry to long-term memory."""
        entry = MemoryEntry(content, memory_type, metadata)
        self.long_term_memory.append(entry)
        
        # Keep long-term memory within limits
        if len(self.long_term_memory) > Config.LONG_TERM_MEMORY_SIZE:
            self.long_term_memory = self.long_term_memory[-Config.LONG_TERM_MEMORY_SIZE:]
        
        self.logger.log_memory_update("long_term", "add", content)
        self._save_memory()
    
    def add_conversation_turn(self, role: str, content: str):
        """Add a conversation turn to history."""
        turn = {
            "role": role,
            "content": content,
            "timestamp": datetime.now().isoformat()
        }
        self.conversation_history.append(turn)
        self.logger.log_memory_update("conversation", "add", f"{role}: {content[:50]}...")
        self._save_memory()
    
    def get_recent_memories(self, count: int = 5) -> List[MemoryEntry]:
        """Get recent memories from short-term memory."""
        return list(self.short_term_memory)[-count:]
    
    def get_relevant_long_term_memories(self, query: str, count: int = 3) -> List[MemoryEntry]:
        """Get relevant long-term memories based on query (simple keyword matching)."""
        query_words = query.lower().split()
        scored_memories = []
        
        for memory in self.long_term_memory:
            score = 0
            content_lower = memory.content.lower()
            for word in query_words:
                if word in content_lower:
                    score += 1
            
            if score > 0:
                scored_memories.append((score, memory))
        
        # Sort by score and return top matches
        scored_memories.sort(key=lambda x: x[0], reverse=True)
        return [memory for _, memory in scored_memories[:count]]
    
    def get_conversation_context(self, max_turns: int = 10) -> List[Dict[str, str]]:
        """Get recent conversation context."""
        return self.conversation_history[-max_turns:]
    
    def consolidate_memories(self):
        """Move important short-term memories to long-term storage."""
        important_memories = []
        
        for memory in self.short_term_memory:
            # Simple heuristic: longer content or specific types are considered important
            if (len(memory.content) > 100 or 
                memory.memory_type in ["decision", "insight", "error", "solution"]):
                important_memories.append(memory)
        
        for memory in important_memories:
            self.add_long_term_memory(
                memory.content, 
                memory.memory_type, 
                memory.metadata
            )
        
        self.logger.log_memory_update("consolidation", "completed", f"Moved {len(important_memories)} memories")
    
    def clear_short_term_memory(self):
        """Clear short-term memory."""
        self.short_term_memory.clear()
        self.logger.log_memory_update("short_term", "clear", "Cleared all short-term memories")
        self._save_memory()
    
    def _save_memory(self):
        """Save memory to disk."""
        try:
            # Save memory entries
            memory_data = {
                "short_term": [entry.to_dict() for entry in self.short_term_memory],
                "long_term": [entry.to_dict() for entry in self.long_term_memory],
                "last_updated": datetime.now().isoformat()
            }
            
            with open(self.memory_file, 'w', encoding='utf-8') as f:
                json.dump(memory_data, f, indent=2, ensure_ascii=False)
            
            # Save conversation history
            with open(self.conversation_file, 'w', encoding='utf-8') as f:
                json.dump(self.conversation_history, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            self.logger.log_error(f"Failed to save memory: {str(e)}", e)
    
    def _load_memory(self):
        """Load memory from disk."""
        try:
            # Load memory entries
            if os.path.exists(self.memory_file):
                with open(self.memory_file, 'r', encoding='utf-8') as f:
                    memory_data = json.load(f)
                
                # Load short-term memory
                for entry_data in memory_data.get("short_term", []):
                    entry = MemoryEntry.from_dict(entry_data)
                    self.short_term_memory.append(entry)
                
                # Load long-term memory
                for entry_data in memory_data.get("long_term", []):
                    entry = MemoryEntry.from_dict(entry_data)
                    self.long_term_memory.append(entry)
            
            # Load conversation history
            if os.path.exists(self.conversation_file):
                with open(self.conversation_file, 'r', encoding='utf-8') as f:
                    self.conversation_history = json.load(f)
                    
        except Exception as e:
            self.logger.log_error(f"Failed to load memory: {str(e)}", e)
    
    def get_memory_summary(self) -> Dict[str, Any]:
        """Get a summary of current memory state."""
        return {
            "agent_name": self.agent_name,
            "short_term_count": len(self.short_term_memory),
            "long_term_count": len(self.long_term_memory),
            "conversation_turns": len(self.conversation_history),
            "last_activity": self.conversation_history[-1]["timestamp"] if self.conversation_history else None
        }
