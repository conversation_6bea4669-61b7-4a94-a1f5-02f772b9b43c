[{"id": "fb04084d-8ecb-4d10-aa4d-46232cdf706a", "amount": 25.5, "description": "Grocery shopping", "category": "Food", "date": "2025-05-29T14:37:34.958797", "created_at": "2025-05-30T14:37:34.958809"}, {"id": "dfaec7c1-5dd5-4500-93b4-57c23b7cd9f6", "amount": 45.0, "description": "Gas station", "category": "Transportation", "date": "2025-05-28T14:37:34.958810", "created_at": "2025-05-30T14:37:34.958815"}, {"id": "e0cafade-6677-4b7c-aa0b-6d17be06fd55", "amount": 12.99, "description": "Netflix subscription", "category": "Entertainment", "date": "2025-05-27T14:37:34.958815", "created_at": "2025-05-30T14:37:34.958819"}, {"id": "5da2a193-05bb-4286-97ed-df87ef84cbb6", "amount": 150.0, "description": "Electric bill", "category": "Utilities", "date": "2025-05-25T14:37:34.958819", "created_at": "2025-05-30T14:37:34.958825"}, {"id": "6ef13d96-c2e3-4432-a1d0-1829564a423f", "amount": 8.5, "description": "Coffee", "category": "Food", "date": "2025-05-29T14:37:34.958826", "created_at": "2025-05-30T14:37:34.958829"}, {"id": "d823e9aa-1a79-4a3d-8d3a-8e291fa97ddd", "amount": 75.0, "description": "Dinner out", "category": "Food", "date": "2025-05-26T14:37:34.958829", "created_at": "2025-05-30T14:37:34.958833"}]