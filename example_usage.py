#!/usr/bin/env python3
"""Example usage of the multi-agent system."""

import sys
import os
from pathlib import Path

# Add the current directory to the Python path
sys.path.insert(0, str(Path(__file__).parent))

def example_cli_usage():
    """Example of using the system via CLI."""
    print("🔧 CLI Usage Example")
    print("=" * 30)
    
    # Example requests
    examples = [
        {
            "request": "Create a Python web scraper that extracts product information from e-commerce websites and saves the data to a CSV file. Include error handling and rate limiting.",
            "folder": None
        },
        {
            "request": "Build a FastAPI REST API for a todo list application with CRUD operations, user authentication, and SQLite database integration.",
            "folder": None
        },
        {
            "request": "Analyze and improve the existing codebase in the specified folder, focusing on code quality and performance optimizations.",
            "folder": "/path/to/your/project"
        }
    ]
    
    print("Example CLI commands:")
    print()
    
    for i, example in enumerate(examples, 1):
        print(f"Example {i}:")
        print(f"Request: {example['request']}")
        
        if example['folder']:
            cmd = f'python main.py --mode cli --request "{example["request"]}" --folder "{example["folder"]}"'
        else:
            cmd = f'python main.py --mode cli --request "{example["request"]}"'
        
        print(f"Command: {cmd}")
        print()

def example_gui_usage():
    """Example of using the system via GUI."""
    print("🖥️ GUI Usage Example")
    print("=" * 30)
    
    print("1. Start the GUI:")
    print("   python main.py --mode gui")
    print()
    print("2. Open your browser to: http://localhost:8501")
    print()
    print("3. In the GUI:")
    print("   - Enter your project request in the text area")
    print("   - Optionally specify a folder path to analyze")
    print("   - Click 'Execute Workflow' to start the process")
    print("   - View results in the tabs below")
    print()

def example_programmatic_usage():
    """Example of using the system programmatically."""
    print("💻 Programmatic Usage Example")
    print("=" * 30)
    
    example_code = '''
from workflow.agent_workflow import MultiAgentWorkflow

# Create workflow instance
workflow = MultiAgentWorkflow()

# Define your request
user_request = """
Create a Python script that:
1. Reads data from a CSV file
2. Performs basic data analysis (mean, median, mode)
3. Generates visualizations using matplotlib
4. Exports results to a PDF report
"""

# Optional: specify a folder to analyze
folder_path = "/path/to/existing/project"  # or None

# Execute the workflow
result = workflow.execute_workflow(user_request, folder_path)

# Check results
if result["status"] == "completed":
    print("✅ Workflow completed successfully!")
    
    # Access different outputs
    if "project_plan" in result["agent_outputs"]:
        print("📋 Project Plan:")
        print(result["agent_outputs"]["project_plan"])
    
    if "generated_code" in result["agent_outputs"]:
        print("💻 Generated Code:")
        print(result["agent_outputs"]["generated_code"])
    
    if "code_review" in result["agent_outputs"]:
        review = result["agent_outputs"]["code_review"]
        print(f"🔍 Code Review Score: {review['overall_score']}/10")
        print(f"📝 Recommendation: {review['recommendation']}")
    
    if result["final_result"]:
        print("🎯 Final Result:")
        print(result["final_result"])
        
else:
    print("❌ Workflow failed:")
    for error in result.get("errors", []):
        print(f"  - {error}")
'''
    
    print("Example Python code:")
    print(example_code)

def example_configuration():
    """Example configuration setup."""
    print("⚙️ Configuration Example")
    print("=" * 30)
    
    env_example = '''
# VLLM OpenAI Compatible Server Configuration
CODER_MODEL_URL=http://localhost:8001/v1
REVIEWER_MODEL_URL=http://localhost:8002/v1
MANAGER_MODEL_URL=http://localhost:8003/v1

# Model Names (adjust based on your VLLM setup)
CODER_MODEL_NAME=llama-3.1-8b-instruct
REVIEWER_MODEL_NAME=llama-3.1-8b-instruct
MANAGER_MODEL_NAME=llama-3.1-8b-instruct

# Model Parameters
TEMPERATURE=0.7
MAX_TOKENS=2048
TOP_P=0.9
FREQUENCY_PENALTY=0.0
PRESENCE_PENALTY=0.0

# Token Management
MAX_TOKENS_PER_AGENT=8196
TOKEN_BUFFER=200

# Memory Configuration
MEMORY_FOLDER=./memory_logs
SHORT_TERM_MEMORY_SIZE=10
LONG_TERM_MEMORY_SIZE=100

# Logging
LOG_LEVEL=INFO
ENABLE_DETAILED_LOGGING=true
'''
    
    print("Example .env file configuration:")
    print(env_example)
    
    print("VLLM Server Setup Examples:")
    print()
    print("# Start VLLM servers (adjust model paths as needed)")
    print("vllm serve meta-llama/Llama-3.1-8B-Instruct --port 8001 --api-key your-key")
    print("vllm serve meta-llama/Llama-3.1-8B-Instruct --port 8002 --api-key your-key")
    print("vllm serve meta-llama/Llama-3.1-8B-Instruct --port 8003 --api-key your-key")
    print()

def example_workflow_steps():
    """Example of workflow steps."""
    print("🔄 Workflow Steps Example")
    print("=" * 30)
    
    steps = [
        ("1. Initialize", "Set up workflow state and logging"),
        ("2. Plan Workflow", "Manager agent analyzes request and creates execution plan"),
        ("3. Analyze Folder", "Coder agent analyzes project structure (if folder provided)"),
        ("4. Generate Plan", "Coder agent creates detailed project plan"),
        ("5. Generate Code", "Coder agent implements the solution"),
        ("6. Review Code", "Reviewer agent conducts comprehensive code review"),
        ("7. Validate Requirements", "Reviewer agent validates requirements compliance"),
        ("8. Synthesize Result", "Manager agent combines all outputs into final deliverable"),
        ("9. Finalize", "Complete workflow and consolidate memories")
    ]
    
    print("The workflow consists of the following steps:")
    print()
    
    for step, description in steps:
        print(f"{step}: {description}")
    
    print()
    print("Each step produces detailed outputs that are available in the final result.")

def example_memory_management():
    """Example of memory management features."""
    print("🧠 Memory Management Example")
    print("=" * 30)
    
    print("The system maintains comprehensive memory for each agent:")
    print()
    print("📁 Memory Files (stored in memory_logs/):")
    print("  - coder_log.txt - Coder agent interactions")
    print("  - reviewer_log.txt - Reviewer agent interactions") 
    print("  - manager_log.txt - Manager agent interactions")
    print("  - system_log.txt - System-wide events")
    print("  - *_memory.json - Structured memory data")
    print("  - *_conversation.json - Conversation history")
    print()
    print("🔍 Memory Types:")
    print("  - Short-term: Recent interactions (configurable size)")
    print("  - Long-term: Important insights and decisions")
    print("  - Conversation: Complete dialogue history")
    print()
    print("💾 Memory Operations:")
    print("  - Automatic consolidation of important memories")
    print("  - Timestamped logging of all interactions")
    print("  - Retrieval of relevant context for new requests")
    print("  - Memory cleanup and management")

def main():
    """Display all examples."""
    print("🤖 Multi-Agent System Usage Examples")
    print("=" * 50)
    print()
    
    examples = [
        example_configuration,
        example_cli_usage,
        example_gui_usage,
        example_programmatic_usage,
        example_workflow_steps,
        example_memory_management
    ]
    
    for example_func in examples:
        example_func()
        print()
        print("-" * 50)
        print()

if __name__ == "__main__":
    main()
