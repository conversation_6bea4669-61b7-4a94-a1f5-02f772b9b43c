"""Token management utilities for the multi-agent system."""

import tiktoken
from typing import List, Dict, Any
from config import Config

class TokenManager:
    """Manages token counting and limits for agents."""

    def __init__(self, model_name: str = "gpt-3.5-turbo"):
        """Initialize the token manager with a specific model encoding."""
        try:
            self.encoding = tiktoken.encoding_for_model(model_name)
        except KeyError:
            # Fallback to cl100k_base encoding for unknown models
            self.encoding = tiktoken.get_encoding("cl100k_base")

    def count_tokens(self, text: str) -> int:
        """Count the number of tokens in a text string."""
        return len(self.encoding.encode(text))

    def count_message_tokens(self, messages: List[Dict[str, str]]) -> int:
        """Count tokens in a list of messages."""
        total_tokens = 0
        for message in messages:
            # Add tokens for role and content
            total_tokens += self.count_tokens(message.get("role", ""))
            total_tokens += self.count_tokens(message.get("content", ""))
            # Add overhead tokens per message
            total_tokens += 4  # Overhead per message
        return total_tokens

    def truncate_messages(self, messages: List[Dict[str, str]], max_tokens: int = None) -> List[Dict[str, str]]:
        """Truncate messages to fit within token limit, keeping the most recent ones."""
        if not messages:
            return messages

        if max_tokens is None:
            max_tokens = Config.MAX_INPUT_TOKENS

        # Always keep the system message if it exists
        system_messages = [msg for msg in messages if msg.get("role") == "system"]
        other_messages = [msg for msg in messages if msg.get("role") != "system"]

        # Calculate tokens for system messages
        system_tokens = self.count_message_tokens(system_messages)
        available_tokens = max_tokens - system_tokens - Config.TOKEN_BUFFER

        if available_tokens <= 0:
            # If system message is too long, truncate it too
            if system_messages:
                truncated_system = self.truncate_text(system_messages[0]["content"], max_tokens - Config.TOKEN_BUFFER)
                return [{"role": "system", "content": truncated_system}]
            return []

        # Add messages from the end until we hit the token limit
        truncated_messages = []
        current_tokens = 0

        for message in reversed(other_messages):
            message_tokens = self.count_message_tokens([message])
            if current_tokens + message_tokens <= available_tokens:
                truncated_messages.insert(0, message)
                current_tokens += message_tokens
            else:
                # Try to fit a truncated version of this message
                remaining_tokens = available_tokens - current_tokens
                if remaining_tokens > 100:  # Only if we have reasonable space
                    truncated_content = self.truncate_text(message["content"], remaining_tokens - 50)
                    if truncated_content:
                        truncated_messages.insert(0, {"role": message["role"], "content": truncated_content})
                break

        return system_messages + truncated_messages

    def truncate_text(self, text: str, max_tokens: int) -> str:
        """Truncate text to fit within token limit."""
        tokens = self.encoding.encode(text)
        if len(tokens) <= max_tokens:
            return text

        # Truncate tokens and decode back to text
        truncated_tokens = tokens[:max_tokens - Config.TOKEN_BUFFER]
        return self.encoding.decode(truncated_tokens)

    def estimate_response_tokens(self, prompt_tokens: int, max_response_tokens: int = None) -> int:
        """Estimate how many tokens are available for the response."""
        if max_response_tokens is None:
            max_response_tokens = Config.MAX_TOKENS

        available_tokens = Config.MAX_TOKENS_PER_AGENT - prompt_tokens - Config.TOKEN_BUFFER
        return min(available_tokens, max_response_tokens)

    def is_within_limit(self, text: str, max_tokens: int = None) -> bool:
        """Check if text is within token limit."""
        if max_tokens is None:
            max_tokens = Config.MAX_INPUT_TOKENS

        return self.count_tokens(text) <= max_tokens

    def create_summary(self, text: str, max_summary_tokens: int = 500) -> str:
        """Create a summary of text that fits within token limit."""
        if self.count_tokens(text) <= max_summary_tokens:
            return text

        # Simple truncation-based summary - take first and last parts
        tokens = self.encoding.encode(text)

        # Take first 60% and last 40% of available tokens
        first_part_tokens = int(max_summary_tokens * 0.6)
        last_part_tokens = max_summary_tokens - first_part_tokens - 10  # Leave space for separator

        first_part = self.encoding.decode(tokens[:first_part_tokens])
        last_part = self.encoding.decode(tokens[-last_part_tokens:])

        return f"{first_part}\n\n[... content truncated ...]\n\n{last_part}"

    def prepare_agent_input(self, system_prompt: str, user_message: str, context: str = "") -> str:
        """Prepare input for an agent, ensuring it fits within token limits."""
        # Calculate available tokens for each component
        total_available = Config.MAX_INPUT_TOKENS

        # Reserve tokens for system prompt (priority)
        system_tokens = self.count_tokens(system_prompt)
        if system_tokens > total_available * 0.3:  # Max 30% for system prompt
            system_prompt = self.truncate_text(system_prompt, int(total_available * 0.3))
            system_tokens = self.count_tokens(system_prompt)

        # Reserve tokens for user message (priority)
        user_tokens = self.count_tokens(user_message)
        if user_tokens > total_available * 0.4:  # Max 40% for user message
            user_message = self.truncate_text(user_message, int(total_available * 0.4))
            user_tokens = self.count_tokens(user_message)

        # Use remaining tokens for context
        remaining_tokens = total_available - system_tokens - user_tokens - Config.TOKEN_BUFFER
        if remaining_tokens > 0 and context:
            context = self.truncate_text(context, remaining_tokens)
        else:
            context = ""

        # Combine all parts
        if context:
            full_input = f"{system_prompt}\n\nContext:\n{context}\n\nUser: {user_message}\n\nAssistant:"
        else:
            full_input = f"{system_prompt}\n\nUser: {user_message}\n\nAssistant:"

        return full_input
