"""Token management utilities for the multi-agent system."""

import tiktoken
from typing import List, Dict, Any
from config import Config

class TokenManager:
    """Manages token counting and limits for agents."""
    
    def __init__(self, model_name: str = "gpt-3.5-turbo"):
        """Initialize the token manager with a specific model encoding."""
        try:
            self.encoding = tiktoken.encoding_for_model(model_name)
        except KeyError:
            # Fallback to cl100k_base encoding for unknown models
            self.encoding = tiktoken.get_encoding("cl100k_base")
    
    def count_tokens(self, text: str) -> int:
        """Count the number of tokens in a text string."""
        return len(self.encoding.encode(text))
    
    def count_message_tokens(self, messages: List[Dict[str, str]]) -> int:
        """Count tokens in a list of messages."""
        total_tokens = 0
        for message in messages:
            # Add tokens for role and content
            total_tokens += self.count_tokens(message.get("role", ""))
            total_tokens += self.count_tokens(message.get("content", ""))
            # Add overhead tokens per message
            total_tokens += 4  # Overhead per message
        return total_tokens
    
    def truncate_messages(self, messages: List[Dict[str, str]], max_tokens: int) -> List[Dict[str, str]]:
        """Truncate messages to fit within token limit, keeping the most recent ones."""
        if not messages:
            return messages
        
        # Always keep the system message if it exists
        system_messages = [msg for msg in messages if msg.get("role") == "system"]
        other_messages = [msg for msg in messages if msg.get("role") != "system"]
        
        # Calculate tokens for system messages
        system_tokens = self.count_message_tokens(system_messages)
        available_tokens = max_tokens - system_tokens - Config.TOKEN_BUFFER
        
        if available_tokens <= 0:
            return system_messages
        
        # Add messages from the end until we hit the token limit
        truncated_messages = []
        current_tokens = 0
        
        for message in reversed(other_messages):
            message_tokens = self.count_message_tokens([message])
            if current_tokens + message_tokens <= available_tokens:
                truncated_messages.insert(0, message)
                current_tokens += message_tokens
            else:
                break
        
        return system_messages + truncated_messages
    
    def truncate_text(self, text: str, max_tokens: int) -> str:
        """Truncate text to fit within token limit."""
        tokens = self.encoding.encode(text)
        if len(tokens) <= max_tokens:
            return text
        
        # Truncate tokens and decode back to text
        truncated_tokens = tokens[:max_tokens - Config.TOKEN_BUFFER]
        return self.encoding.decode(truncated_tokens)
    
    def estimate_response_tokens(self, prompt_tokens: int, max_response_tokens: int = None) -> int:
        """Estimate how many tokens are available for the response."""
        if max_response_tokens is None:
            max_response_tokens = Config.MAX_TOKENS
        
        available_tokens = Config.MAX_TOKENS_PER_AGENT - prompt_tokens - Config.TOKEN_BUFFER
        return min(available_tokens, max_response_tokens)
    
    def is_within_limit(self, text: str, max_tokens: int = None) -> bool:
        """Check if text is within token limit."""
        if max_tokens is None:
            max_tokens = Config.MAX_TOKENS_PER_AGENT
        
        return self.count_tokens(text) <= max_tokens
