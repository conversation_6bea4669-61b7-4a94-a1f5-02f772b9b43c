"""Logging utilities for the multi-agent system."""

import logging
import os
from datetime import datetime
from typing import Any, Dict
from config import Config

class AgentLogger:
    """Custom logger for agent interactions and memory."""
    
    def __init__(self, agent_name: str):
        """Initialize logger for a specific agent."""
        self.agent_name = agent_name
        self.logger = logging.getLogger(f"agent_{agent_name}")
        self.logger.setLevel(getattr(logging, Config.LOG_LEVEL))
        
        # Ensure memory folder exists
        Config.ensure_memory_folder()
        
        # Create file handler for this agent
        log_file = os.path.join(Config.MEMORY_FOLDER, f"{agent_name}_log.txt")
        file_handler = logging.FileHandler(log_file, mode='a', encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        
        # Create console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(getattr(logging, Config.LOG_LEVEL))
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # Add handlers to logger
        if not self.logger.handlers:
            self.logger.addHandler(file_handler)
            self.logger.addHandler(console_handler)
    
    def log_interaction(self, interaction_type: str, content: str, metadata: Dict[str, Any] = None):
        """Log an agent interaction with timestamp."""
        timestamp = datetime.now().isoformat()
        
        log_entry = {
            "timestamp": timestamp,
            "agent": self.agent_name,
            "type": interaction_type,
            "content": content,
            "metadata": metadata or {}
        }
        
        # Log to file
        self.logger.info(f"[{interaction_type}] {content}")
        
        if Config.ENABLE_DETAILED_LOGGING and metadata:
            self.logger.debug(f"Metadata: {metadata}")
    
    def log_memory_update(self, memory_type: str, operation: str, content: str):
        """Log memory operations."""
        self.log_interaction(
            "MEMORY_UPDATE",
            f"{memory_type} - {operation}: {content[:100]}...",
            {"memory_type": memory_type, "operation": operation}
        )
    
    def log_tool_usage(self, tool_name: str, input_data: Any, output_data: Any):
        """Log tool usage."""
        self.log_interaction(
            "TOOL_USAGE",
            f"Used tool: {tool_name}",
            {
                "tool_name": tool_name,
                "input": str(input_data)[:200],
                "output": str(output_data)[:200]
            }
        )
    
    def log_error(self, error_message: str, exception: Exception = None):
        """Log errors."""
        self.logger.error(f"Error: {error_message}")
        if exception and Config.ENABLE_DETAILED_LOGGING:
            self.logger.exception(exception)
    
    def log_workflow_step(self, step_name: str, status: str, details: str = ""):
        """Log workflow steps."""
        self.log_interaction(
            "WORKFLOW_STEP",
            f"{step_name}: {status}",
            {"step": step_name, "status": status, "details": details}
        )

class SystemLogger:
    """System-wide logger for workflow coordination."""
    
    def __init__(self):
        """Initialize system logger."""
        self.logger = logging.getLogger("system")
        self.logger.setLevel(getattr(logging, Config.LOG_LEVEL))
        
        # Ensure memory folder exists
        Config.ensure_memory_folder()
        
        # Create file handler for system logs
        log_file = os.path.join(Config.MEMORY_FOLDER, "system_log.txt")
        file_handler = logging.FileHandler(log_file, mode='a', encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        
        # Create console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(getattr(logging, Config.LOG_LEVEL))
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # Add handlers to logger
        if not self.logger.handlers:
            self.logger.addHandler(file_handler)
            self.logger.addHandler(console_handler)
    
    def log_workflow_start(self, workflow_id: str, user_request: str):
        """Log the start of a workflow."""
        self.logger.info(f"Workflow {workflow_id} started: {user_request}")
    
    def log_workflow_end(self, workflow_id: str, status: str, result: str = ""):
        """Log the end of a workflow."""
        self.logger.info(f"Workflow {workflow_id} ended with status: {status}")
        if result:
            self.logger.info(f"Result: {result[:200]}...")
    
    def log_agent_handoff(self, from_agent: str, to_agent: str, context: str):
        """Log agent handoffs."""
        self.logger.info(f"Handoff from {from_agent} to {to_agent}: {context}")
    
    def log_system_error(self, error_message: str, exception: Exception = None):
        """Log system errors."""
        self.logger.error(f"System error: {error_message}")
        if exception and Config.ENABLE_DETAILED_LOGGING:
            self.logger.exception(exception)
