"""Main application for the personal finance tracker."""

import click
from datetime import datetime, timed<PERSON>ta
from typing import List
import sys
import os

# Add the src directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from models.expense import Expense
from models.budget import Budget
from utils.data_handler import DataHandler

# Initialize data handler
data_handler = DataHandler(data_dir="../data")

@click.group()
def cli():
    """Personal Finance Tracker - Manage your expenses and budgets."""
    pass

@cli.command()
@click.option('--amount', '-a', type=float, required=True, help='Expense amount')
@click.option('--description', '-d', required=True, help='Expense description')
@click.option('--category', '-c', default='Other', help='Expense category')
@click.option('--date', type=click.DateTime(['%Y-%m-%d']), help='Expense date (YYYY-MM-DD)')
def add_expense(amount: float, description: str, category: str, date: datetime):
    """Add a new expense."""
    expense = Expense(
        amount=amount,
        description=description,
        category=category,
        date=date
    )
    
    data_handler.add_expense(expense)
    click.echo(f"✅ Added expense: {expense}")

@cli.command()
def list_expenses():
    """List all expenses."""
    expenses = data_handler.load_expenses()
    
    if not expenses:
        click.echo("No expenses found.")
        return
    
    click.echo("\n📊 All Expenses:")
    click.echo("-" * 50)
    
    total = 0
    for expense in sorted(expenses, key=lambda x: x.date, reverse=True):
        click.echo(f"{expense.date.strftime('%Y-%m-%d')} | ${expense.amount:8.2f} | {expense.category:12} | {expense.description}")
        total += expense.amount
    
    click.echo("-" * 50)
    click.echo(f"Total: ${total:.2f}")

@cli.command()
@click.option('--category', '-c', required=True, help='Budget category')
@click.option('--amount', '-a', type=float, required=True, help='Budget amount')
@click.option('--period', '-p', default='monthly', help='Budget period (monthly, weekly, yearly)')
def add_budget(category: str, amount: float, period: str):
    """Add a new budget."""
    budget = Budget(
        category=category,
        amount=amount,
        period=period
    )
    
    data_handler.add_budget(budget)
    click.echo(f"✅ Added budget: {budget}")

@cli.command()
def list_budgets():
    """List all budgets with current spending."""
    budgets = data_handler.load_budgets()
    
    if not budgets:
        click.echo("No budgets found.")
        return
    
    click.echo("\n💰 Budget Status:")
    click.echo("-" * 60)
    
    for budget in budgets:
        # Calculate current spending for this category
        expenses = data_handler.get_expenses_by_category(budget.category)
        current_spent = sum(expense.amount for expense in expenses)
        
        budget.spent = current_spent
        remaining = budget.remaining()
        percentage = budget.percentage_used()
        
        status = "🔴 OVER" if budget.is_over_budget() else "🟢 OK"
        
        click.echo(f"{budget.category:15} | ${current_spent:8.2f} / ${budget.amount:8.2f} | {percentage:5.1f}% | {status}")
    
    click.echo("-" * 60)

@cli.command()
@click.option('--category', '-c', help='Filter by category')
@click.option('--days', '-d', type=int, default=30, help='Number of days to look back')
def summary(category: str, days: int):
    """Show expense summary."""
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days)
    
    expenses = data_handler.get_expenses_by_date_range(start_date, end_date)
    
    if category:
        expenses = [e for e in expenses if e.category.lower() == category.lower()]
        title = f"📈 {category} Expenses (Last {days} days)"
    else:
        title = f"📈 All Expenses (Last {days} days)"
    
    click.echo(f"\n{title}")
    click.echo("-" * 50)
    
    if not expenses:
        click.echo("No expenses found for the specified criteria.")
        return
    
    # Group by category
    category_totals = {}
    for expense in expenses:
        if expense.category not in category_totals:
            category_totals[expense.category] = 0
        category_totals[expense.category] += expense.amount
    
    total = 0
    for cat, amount in sorted(category_totals.items()):
        click.echo(f"{cat:20} | ${amount:8.2f}")
        total += amount
    
    click.echo("-" * 50)
    click.echo(f"{'Total':20} | ${total:8.2f}")
    click.echo(f"{'Daily Average':20} | ${total/days:8.2f}")

@cli.command()
def categories():
    """List all expense categories."""
    cats = data_handler.get_categories()
    
    if not cats:
        click.echo("No categories found.")
        return
    
    click.echo("\n📂 Expense Categories:")
    for i, category in enumerate(cats, 1):
        expenses = data_handler.get_expenses_by_category(category)
        total = sum(expense.amount for expense in expenses)
        click.echo(f"{i:2}. {category:20} ({len(expenses)} expenses, ${total:.2f})")

@cli.command()
@click.confirmation_option(prompt='Are you sure you want to delete all data?')
def clear_data():
    """Clear all expenses and budgets (use with caution!)."""
    data_handler.save_expenses([])
    data_handler.save_budgets([])
    click.echo("✅ All data cleared.")

@cli.command()
def demo():
    """Add some demo data for testing."""
    demo_expenses = [
        Expense(25.50, "Grocery shopping", "Food", datetime.now() - timedelta(days=1)),
        Expense(45.00, "Gas station", "Transportation", datetime.now() - timedelta(days=2)),
        Expense(12.99, "Netflix subscription", "Entertainment", datetime.now() - timedelta(days=3)),
        Expense(150.00, "Electric bill", "Utilities", datetime.now() - timedelta(days=5)),
        Expense(8.50, "Coffee", "Food", datetime.now() - timedelta(days=1)),
        Expense(75.00, "Dinner out", "Food", datetime.now() - timedelta(days=4)),
    ]
    
    demo_budgets = [
        Budget("Food", 300.00, "monthly"),
        Budget("Transportation", 200.00, "monthly"),
        Budget("Entertainment", 100.00, "monthly"),
        Budget("Utilities", 250.00, "monthly"),
    ]
    
    for expense in demo_expenses:
        data_handler.add_expense(expense)
    
    for budget in demo_budgets:
        data_handler.add_budget(budget)
    
    click.echo("✅ Demo data added successfully!")
    click.echo("Try running: python main.py list-expenses")
    click.echo("Or: python main.py list-budgets")

if __name__ == '__main__':
    cli()
