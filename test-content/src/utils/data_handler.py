"""Data handling utilities for the personal finance tracker."""

import json
import os
from typing import List, Dict, Any, Optional
from datetime import datetime

from models.expense import Expense
from models.budget import Budget

class DataHandler:
    """Handles data persistence for expenses and budgets."""
    
    def __init__(self, data_dir: str = "data"):
        """Initialize data handler.
        
        Args:
            data_dir: Directory to store data files
        """
        self.data_dir = data_dir
        self.expenses_file = os.path.join(data_dir, "expenses.json")
        self.budgets_file = os.path.join(data_dir, "budgets.json")
        
        # Create data directory if it doesn't exist
        os.makedirs(data_dir, exist_ok=True)
        
        # Initialize files if they don't exist
        self._init_files()
    
    def _init_files(self):
        """Initialize data files if they don't exist."""
        if not os.path.exists(self.expenses_file):
            self._save_json(self.expenses_file, [])
        
        if not os.path.exists(self.budgets_file):
            self._save_json(self.budgets_file, [])
    
    def _load_json(self, filepath: str) -> List[Dict[str, Any]]:
        """Load JSON data from file."""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return []
    
    def _save_json(self, filepath: str, data: List[Dict[str, Any]]):
        """Save JSON data to file."""
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
    
    # Expense methods
    def load_expenses(self) -> List[Expense]:
        """Load all expenses from file."""
        data = self._load_json(self.expenses_file)
        return [Expense.from_dict(item) for item in data]
    
    def save_expenses(self, expenses: List[Expense]):
        """Save expenses to file."""
        data = [expense.to_dict() for expense in expenses]
        self._save_json(self.expenses_file, data)
    
    def add_expense(self, expense: Expense):
        """Add a single expense."""
        expenses = self.load_expenses()
        expenses.append(expense)
        self.save_expenses(expenses)
    
    def get_expense_by_id(self, expense_id: str) -> Optional[Expense]:
        """Get expense by ID."""
        expenses = self.load_expenses()
        for expense in expenses:
            if expense.id == expense_id:
                return expense
        return None
    
    def delete_expense(self, expense_id: str) -> bool:
        """Delete expense by ID."""
        expenses = self.load_expenses()
        original_count = len(expenses)
        expenses = [e for e in expenses if e.id != expense_id]
        
        if len(expenses) < original_count:
            self.save_expenses(expenses)
            return True
        return False
    
    # Budget methods
    def load_budgets(self) -> List[Budget]:
        """Load all budgets from file."""
        data = self._load_json(self.budgets_file)
        return [Budget.from_dict(item) for item in data]
    
    def save_budgets(self, budgets: List[Budget]):
        """Save budgets to file."""
        data = [budget.to_dict() for budget in budgets]
        self._save_json(self.budgets_file, data)
    
    def add_budget(self, budget: Budget):
        """Add a single budget."""
        budgets = self.load_budgets()
        budgets.append(budget)
        self.save_budgets(budgets)
    
    def get_budget_by_category(self, category: str) -> Optional[Budget]:
        """Get budget by category."""
        budgets = self.load_budgets()
        for budget in budgets:
            if budget.category.lower() == category.lower():
                return budget
        return None
    
    # Utility methods
    def get_expenses_by_category(self, category: str) -> List[Expense]:
        """Get all expenses for a specific category."""
        expenses = self.load_expenses()
        return [e for e in expenses if e.category.lower() == category.lower()]
    
    def get_expenses_by_date_range(self, start_date: datetime, end_date: datetime) -> List[Expense]:
        """Get expenses within a date range."""
        expenses = self.load_expenses()
        return [e for e in expenses if start_date <= e.date <= end_date]
    
    def get_total_expenses(self) -> float:
        """Get total of all expenses."""
        expenses = self.load_expenses()
        return sum(expense.amount for expense in expenses)
    
    def get_categories(self) -> List[str]:
        """Get all unique expense categories."""
        expenses = self.load_expenses()
        categories = set(expense.category for expense in expenses)
        return sorted(list(categories))
    
    def backup_data(self, backup_dir: str):
        """Create a backup of all data."""
        os.makedirs(backup_dir, exist_ok=True)
        
        # Copy expenses
        expenses_backup = os.path.join(backup_dir, f"expenses_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        expenses = self.load_expenses()
        data = [expense.to_dict() for expense in expenses]
        self._save_json(expenses_backup, data)
        
        # Copy budgets
        budgets_backup = os.path.join(backup_dir, f"budgets_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        budgets = self.load_budgets()
        data = [budget.to_dict() for budget in budgets]
        self._save_json(budgets_backup, data)
