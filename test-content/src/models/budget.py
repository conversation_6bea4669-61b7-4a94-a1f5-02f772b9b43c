"""Budget model for the personal finance tracker."""

from datetime import datetime
from typing import Dict, Any, Optional
import uuid

class Budget:
    """Represents a budget for a specific category and time period."""
    
    def __init__(self, category: str, amount: float, period: str = "monthly",
                 start_date: Optional[datetime] = None, budget_id: Optional[str] = None):
        """Initialize a budget.
        
        Args:
            category: Budget category
            amount: Budget amount
            period: Budget period (monthly, weekly, yearly)
            start_date: Start date of the budget period
            budget_id: Unique ID (auto-generated if not provided)
        """
        self.id = budget_id or str(uuid.uuid4())
        self.category = category
        self.amount = float(amount)
        self.period = period
        self.start_date = start_date or datetime.now()
        self.created_at = datetime.now()
        self.spent = 0.0
    
    def add_expense(self, expense_amount: float):
        """Add an expense to this budget."""
        self.spent += expense_amount
    
    def remaining(self) -> float:
        """Calculate remaining budget amount."""
        return self.amount - self.spent
    
    def percentage_used(self) -> float:
        """Calculate percentage of budget used."""
        if self.amount == 0:
            return 0.0
        return (self.spent / self.amount) * 100
    
    def is_over_budget(self) -> bool:
        """Check if budget is exceeded."""
        return self.spent > self.amount
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert budget to dictionary."""
        return {
            "id": self.id,
            "category": self.category,
            "amount": self.amount,
            "period": self.period,
            "start_date": self.start_date.isoformat(),
            "created_at": self.created_at.isoformat(),
            "spent": self.spent
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Budget':
        """Create budget from dictionary."""
        budget = cls(
            category=data["category"],
            amount=data["amount"],
            period=data.get("period", "monthly"),
            start_date=datetime.fromisoformat(data["start_date"]),
            budget_id=data["id"]
        )
        if "created_at" in data:
            budget.created_at = datetime.fromisoformat(data["created_at"])
        if "spent" in data:
            budget.spent = data["spent"]
        return budget
    
    def __str__(self) -> str:
        """String representation of budget."""
        return f"{self.category}: ${self.spent:.2f} / ${self.amount:.2f} ({self.percentage_used():.1f}%)"
    
    def __repr__(self) -> str:
        """Detailed representation of budget."""
        return f"Budget(id='{self.id}', category='{self.category}', amount={self.amount}, spent={self.spent})"
