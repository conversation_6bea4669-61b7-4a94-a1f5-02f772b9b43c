"""Expense model for the personal finance tracker."""

from datetime import datetime
from typing import Optional, Dict, Any
import uuid

class Expense:
    """Represents a single expense entry."""
    
    def __init__(self, amount: float, description: str, category: str = "Other", 
                 date: Optional[datetime] = None, expense_id: Optional[str] = None):
        """Initialize an expense.
        
        Args:
            amount: The expense amount
            description: Description of the expense
            category: Category of the expense
            date: Date of the expense (defaults to now)
            expense_id: Unique ID (auto-generated if not provided)
        """
        self.id = expense_id or str(uuid.uuid4())
        self.amount = float(amount)
        self.description = description
        self.category = category
        self.date = date or datetime.now()
        self.created_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert expense to dictionary."""
        return {
            "id": self.id,
            "amount": self.amount,
            "description": self.description,
            "category": self.category,
            "date": self.date.isoformat(),
            "created_at": self.created_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Expense':
        """Create expense from dictionary."""
        expense = cls(
            amount=data["amount"],
            description=data["description"],
            category=data.get("category", "Other"),
            date=datetime.fromisoformat(data["date"]),
            expense_id=data["id"]
        )
        if "created_at" in data:
            expense.created_at = datetime.fromisoformat(data["created_at"])
        return expense
    
    def __str__(self) -> str:
        """String representation of expense."""
        return f"${self.amount:.2f} - {self.description} ({self.category}) on {self.date.strftime('%Y-%m-%d')}"
    
    def __repr__(self) -> str:
        """Detailed representation of expense."""
        return f"Expense(id='{self.id}', amount={self.amount}, description='{self.description}', category='{self.category}', date='{self.date}')"
