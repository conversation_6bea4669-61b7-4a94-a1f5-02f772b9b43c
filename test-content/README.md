# Personal Finance Tracker

A simple personal finance tracking application that helps users manage their income, expenses, and budgets.

## Current Features

- Basic expense tracking
- Simple data storage
- Command-line interface

## Planned Features

- Web dashboard
- Budget management
- Expense categorization
- Monthly/yearly reports
- Data visualization
- Export functionality

## Project Structure

```
personal-finance-tracker/
├── src/
│   ├── models/
│   │   ├── expense.py
│   │   └── budget.py
│   ├── utils/
│   │   └── data_handler.py
│   └── main.py
├── data/
│   └── expenses.json
├── requirements.txt
└── README.md
```

## Installation

```bash
pip install -r requirements.txt
python src/main.py
```

## Usage

The application currently supports basic expense tracking through a command-line interface.

## TODO

- [ ] Add web interface
- [ ] Implement budget tracking
- [ ] Add data visualization
- [ ] Create expense categories
- [ ] Add data export features
- [ ] Implement user authentication
- [ ] Add database support
- [ ] Create mobile app
