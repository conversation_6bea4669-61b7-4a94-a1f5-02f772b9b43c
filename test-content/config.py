"""Configuration settings for the personal finance tracker."""

import os
from pathlib import Path

# Base directory
BASE_DIR = Path(__file__).parent

# Data directory
DATA_DIR = BASE_DIR / "data"

# Default categories
DEFAULT_CATEGORIES = [
    "Food",
    "Transportation", 
    "Entertainment",
    "Utilities",
    "Healthcare",
    "Shopping",
    "Education",
    "Other"
]

# Budget periods
BUDGET_PERIODS = [
    "weekly",
    "monthly", 
    "quarterly",
    "yearly"
]

# File paths
EXPENSES_FILE = DATA_DIR / "expenses.json"
BUDGETS_FILE = DATA_DIR / "budgets.json"
BACKUP_DIR = DATA_DIR / "backups"

# Display settings
CURRENCY_SYMBOL = "$"
DATE_FORMAT = "%Y-%m-%d"
DATETIME_FORMAT = "%Y-%m-%d %H:%M:%S"

# Validation settings
MAX_EXPENSE_AMOUNT = 10000.00
MIN_EXPENSE_AMOUNT = 0.01
MAX_DESCRIPTION_LENGTH = 200

# Application settings
APP_NAME = "Personal Finance Tracker"
APP_VERSION = "1.0.0"
DEBUG = os.getenv("DEBUG", "False").lower() == "true"
