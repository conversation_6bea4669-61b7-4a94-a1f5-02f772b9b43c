"""Tests for the Expense model."""

import unittest
from datetime import datetime
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from models.expense import Expense

class TestExpense(unittest.TestCase):
    """Test cases for the Expense class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.test_date = datetime(2024, 1, 15, 10, 30, 0)
        self.expense = Expense(
            amount=25.50,
            description="Test expense",
            category="Food",
            date=self.test_date
        )
    
    def test_expense_creation(self):
        """Test expense creation."""
        self.assertEqual(self.expense.amount, 25.50)
        self.assertEqual(self.expense.description, "Test expense")
        self.assertEqual(self.expense.category, "Food")
        self.assertEqual(self.expense.date, self.test_date)
        self.assertIsNotNone(self.expense.id)
        self.assertIsInstance(self.expense.created_at, datetime)
    
    def test_expense_to_dict(self):
        """Test expense to dictionary conversion."""
        expense_dict = self.expense.to_dict()
        
        self.assertIn("id", expense_dict)
        self.assertEqual(expense_dict["amount"], 25.50)
        self.assertEqual(expense_dict["description"], "Test expense")
        self.assertEqual(expense_dict["category"], "Food")
        self.assertIn("date", expense_dict)
        self.assertIn("created_at", expense_dict)
    
    def test_expense_from_dict(self):
        """Test expense creation from dictionary."""
        expense_dict = self.expense.to_dict()
        new_expense = Expense.from_dict(expense_dict)
        
        self.assertEqual(new_expense.id, self.expense.id)
        self.assertEqual(new_expense.amount, self.expense.amount)
        self.assertEqual(new_expense.description, self.expense.description)
        self.assertEqual(new_expense.category, self.expense.category)
        self.assertEqual(new_expense.date, self.expense.date)
    
    def test_expense_str(self):
        """Test string representation."""
        str_repr = str(self.expense)
        self.assertIn("$25.50", str_repr)
        self.assertIn("Test expense", str_repr)
        self.assertIn("Food", str_repr)
        self.assertIn("2024-01-15", str_repr)
    
    def test_default_category(self):
        """Test default category assignment."""
        expense = Expense(10.00, "Test")
        self.assertEqual(expense.category, "Other")
    
    def test_default_date(self):
        """Test default date assignment."""
        expense = Expense(10.00, "Test")
        self.assertIsInstance(expense.date, datetime)

if __name__ == '__main__':
    unittest.main()
