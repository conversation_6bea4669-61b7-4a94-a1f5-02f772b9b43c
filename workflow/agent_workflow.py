"""Multi-agent workflow orchestration using LangGraph."""

import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

from agents.coder_agent import CoderAgent
from agents.reviewer_agent import ReviewerAgent
from agents.manager_agent import ManagerAgent
from utils.logger import SystemLogger
from config import Config

@dataclass
class WorkflowState:
    """State object for the workflow."""
    workflow_id: str
    user_request: str
    folder_path: Optional[str]
    current_step: str
    results: Dict[str, Any]
    errors: List[str]
    completed_steps: List[str]
    agent_outputs: Dict[str, Any]
    final_result: Optional[str]
    status: str  # "running", "completed", "failed"

class MultiAgentWorkflow:
    """Orchestrates the multi-agent workflow for code generation and review."""

    def __init__(self):
        """Initialize the workflow with agents."""
        self.coder_agent = CoderAgent()
        self.reviewer_agent = ReviewerAgent()
        self.manager_agent = ManagerAgent()
        self.system_logger = SystemLogger()

        # Workflow steps
        self.workflow_steps = [
            "initialize",
            "plan_workflow",
            "analyze_folder",
            "generate_plan",
            "generate_code",
            "review_code",
            "validate_requirements",
            "synthesize_result",
            "finalize"
        ]

    def execute_workflow(self, user_request: str, folder_path: str = None) -> Dict[str, Any]:
        """Execute the complete multi-agent workflow."""
        workflow_id = str(uuid.uuid4())[:8]

        # Initialize workflow state
        state = WorkflowState(
            workflow_id=workflow_id,
            user_request=user_request,
            folder_path=folder_path,
            current_step="initialize",
            results={},
            errors=[],
            completed_steps=[],
            agent_outputs={},
            final_result=None,
            status="running"
        )

        self.system_logger.log_workflow_start(workflow_id, user_request)

        try:
            # Execute workflow steps
            for step in self.workflow_steps:
                state.current_step = step
                self.system_logger.log_workflow_start(workflow_id, f"Executing step: {step}")

                # Execute the step
                step_result = self._execute_step(step, state)

                if step_result.get("error"):
                    state.errors.append(f"{step}: {step_result['error']}")
                    self.system_logger.log_system_error(f"Step {step} failed: {step_result['error']}")

                    # Decide whether to continue or abort
                    if step in ["initialize", "plan_workflow"]:
                        # Critical steps - abort workflow
                        state.status = "failed"
                        break
                    else:
                        # Non-critical steps - log error and continue
                        continue

                # Store step results
                state.results[step] = step_result
                state.completed_steps.append(step)

                # Check if we should stop early
                if step_result.get("stop_workflow"):
                    break

            # Finalize workflow
            if state.status == "running":
                state.status = "completed"

            # Consolidate agent memories
            self._consolidate_memories()

            self.system_logger.log_workflow_end(workflow_id, state.status, state.final_result or "No final result")

            return self._format_workflow_result(state)

        except Exception as e:
            state.status = "failed"
            state.errors.append(f"Workflow execution failed: {str(e)}")
            self.system_logger.log_system_error(f"Workflow {workflow_id} failed", e)

            return self._format_workflow_result(state)

    def _execute_step(self, step: str, state: WorkflowState) -> Dict[str, Any]:
        """Execute a specific workflow step."""
        try:
            if step == "initialize":
                return self._step_initialize(state)
            elif step == "plan_workflow":
                return self._step_plan_workflow(state)
            elif step == "analyze_folder":
                return self._step_analyze_folder(state)
            elif step == "generate_plan":
                return self._step_generate_plan(state)
            elif step == "generate_code":
                return self._step_generate_code(state)
            elif step == "review_code":
                return self._step_review_code(state)
            elif step == "validate_requirements":
                return self._step_validate_requirements(state)
            elif step == "synthesize_result":
                return self._step_synthesize_result(state)
            elif step == "finalize":
                return self._step_finalize(state)
            else:
                return {"error": f"Unknown step: {step}"}

        except Exception as e:
            return {"error": f"Step execution failed: {str(e)}"}

    def _step_initialize(self, state: WorkflowState) -> Dict[str, Any]:
        """Initialize the workflow."""
        return {
            "message": "Workflow initialized",
            "timestamp": datetime.now().isoformat(),
            "workflow_id": state.workflow_id
        }

    def _step_plan_workflow(self, state: WorkflowState) -> Dict[str, Any]:
        """Plan the workflow using the manager agent."""
        plan_result = self.manager_agent.plan_workflow(state.user_request, state.folder_path)

        if "error" in plan_result:
            return {"error": plan_result["error"]}

        state.agent_outputs["manager_plan"] = plan_result

        return {
            "plan": plan_result["structured_plan"],
            "complexity": plan_result["estimated_complexity"],
            "plan_text": plan_result["plan_text"]
        }

    def _step_analyze_folder(self, state: WorkflowState) -> Dict[str, Any]:
        """Analyze the project folder if provided."""
        if not state.folder_path:
            return {"message": "No folder path provided, skipping analysis"}

        analysis_result = self.coder_agent.analyze_project_folder(state.folder_path)

        if "error" in analysis_result:
            return {"error": analysis_result["error"]}

        state.agent_outputs["folder_analysis"] = analysis_result

        return {
            "analysis": analysis_result,
            "project_type": analysis_result.get("summary", {}).get("project_type", "Unknown"),
            "file_count": analysis_result.get("summary", {}).get("total_files", 0)
        }

    def _step_generate_plan(self, state: WorkflowState) -> Dict[str, Any]:
        """Generate a project plan using the coder agent."""
        folder_analysis = state.agent_outputs.get("folder_analysis")

        # Create a summarized version of folder analysis for the coder
        if folder_analysis:
            from utils.token_manager import TokenManager
            token_manager = TokenManager()

            # Create a concise summary of the folder analysis
            analysis_summary = {
                "project_type": folder_analysis.get("summary", {}).get("project_type", "Unknown"),
                "total_files": folder_analysis.get("summary", {}).get("total_files", 0),
                "programming_languages": folder_analysis.get("summary", {}).get("programming_languages", []),
                "file_types": folder_analysis.get("file_types", {}),
                "key_files": list(folder_analysis.get("structure", {}).get("children", []))[:5]  # Top 5 files
            }

            # Use summarized analysis instead of full analysis
            plan_result = self.coder_agent.generate_project_plan(state.user_request, analysis_summary)
        else:
            plan_result = self.coder_agent.generate_project_plan(state.user_request, None)

        if plan_result.startswith("Failed to generate"):
            return {"error": plan_result}

        # Store both full and summarized versions
        state.agent_outputs["project_plan"] = plan_result

        # Create a summary for passing to other agents
        from utils.token_manager import TokenManager
        token_manager = TokenManager()
        plan_summary = token_manager.create_summary(plan_result, 600)
        state.agent_outputs["project_plan_summary"] = plan_summary

        return {
            "project_plan": plan_result,
            "plan_summary": plan_summary,
            "plan_length": len(plan_result)
        }

    def _step_generate_code(self, state: WorkflowState) -> Dict[str, Any]:
        """Generate code using the coder agent."""
        # Use summarized project plan to avoid token limits
        project_plan_summary = state.agent_outputs.get("project_plan_summary", "")

        # Create a concise specification
        from utils.token_manager import TokenManager
        token_manager = TokenManager()

        # Truncate user request if too long
        user_request = token_manager.truncate_text(state.user_request, 800)

        specification = f"User Request: {user_request}\n\nProject Plan Summary:\n{project_plan_summary}"

        code_result = self.coder_agent.generate_code(specification)

        if code_result.startswith("Failed to generate"):
            return {"error": code_result}

        # Store full code and create summary for other agents
        state.agent_outputs["generated_code"] = code_result

        # Create a summary of the generated code for review
        code_summary = token_manager.create_summary(code_result, 800)
        state.agent_outputs["generated_code_summary"] = code_summary

        return {
            "generated_code": code_result,
            "code_summary": code_summary,
            "code_length": len(code_result)
        }

    def _step_review_code(self, state: WorkflowState) -> Dict[str, Any]:
        """Review the generated code using the reviewer agent."""
        generated_code_summary = state.agent_outputs.get("generated_code_summary")

        if not generated_code_summary:
            return {"error": "No code available for review"}

        # Use summarized inputs for the reviewer
        from utils.token_manager import TokenManager
        token_manager = TokenManager()

        user_request_summary = token_manager.truncate_text(state.user_request, 500)
        project_plan_summary = state.agent_outputs.get("project_plan_summary", "")

        review_result = self.reviewer_agent.review_code(
            generated_code_summary,  # Use summary instead of full code
            user_request_summary,
            {"project_plan_summary": project_plan_summary}
        )

        if "error" in review_result:
            return {"error": review_result["error"]}

        state.agent_outputs["code_review"] = review_result

        return {
            "review": review_result,
            "overall_score": review_result["overall_score"],
            "recommendation": review_result["recommendation"]
        }

    def _step_validate_requirements(self, state: WorkflowState) -> Dict[str, Any]:
        """Validate requirements compliance using the reviewer agent."""
        generated_code_summary = state.agent_outputs.get("generated_code_summary")

        if not generated_code_summary:
            return {"error": "No code available for validation"}

        # Use summarized inputs for validation
        from utils.token_manager import TokenManager
        token_manager = TokenManager()

        user_request_summary = token_manager.truncate_text(state.user_request, 800)

        validation_result = self.reviewer_agent.validate_requirements_compliance(
            generated_code_summary,  # Use summary instead of full code
            user_request_summary
        )

        if "error" in validation_result:
            return {"error": validation_result["error"]}

        state.agent_outputs["requirements_validation"] = validation_result

        return {
            "validation": validation_result,
            "compliance_score": validation_result["compliance_score"],
            "compliance_rating": validation_result["compliance_rating"]
        }

    def _step_synthesize_result(self, state: WorkflowState) -> Dict[str, Any]:
        """Synthesize the final result using the manager agent."""
        # Create summarized workflow results for the manager
        from utils.token_manager import TokenManager
        token_manager = TokenManager()

        # Create a concise summary of all agent outputs
        summarized_outputs = {
            "project_plan_summary": state.agent_outputs.get("project_plan_summary", ""),
            "generated_code_summary": state.agent_outputs.get("generated_code_summary", ""),
            "code_review_summary": token_manager.create_summary(
                str(state.agent_outputs.get("code_review", {})), 400
            ),
            "validation_summary": token_manager.create_summary(
                str(state.agent_outputs.get("requirements_validation", {})), 300
            ),
            "folder_analysis_summary": {
                "project_type": state.agent_outputs.get("folder_analysis", {}).get("summary", {}).get("project_type", "Unknown"),
                "total_files": state.agent_outputs.get("folder_analysis", {}).get("summary", {}).get("total_files", 0),
                "programming_languages": state.agent_outputs.get("folder_analysis", {}).get("summary", {}).get("programming_languages", [])
            }
        }

        user_request_summary = token_manager.truncate_text(state.user_request, 600)

        synthesis_result = self.manager_agent.synthesize_final_result(
            summarized_outputs,
            user_request_summary
        )

        if "error" in synthesis_result:
            return {"error": synthesis_result["error"]}

        state.final_result = synthesis_result["final_result"]
        state.agent_outputs["final_synthesis"] = synthesis_result

        return {
            "final_result": synthesis_result["final_result"],
            "quality_score": synthesis_result["quality_score"],
            "final_status": synthesis_result["final_status"]
        }

    def _step_finalize(self, state: WorkflowState) -> Dict[str, Any]:
        """Finalize the workflow."""
        return {
            "message": "Workflow completed successfully",
            "timestamp": datetime.now().isoformat(),
            "total_steps": len(state.completed_steps),
            "errors": len(state.errors)
        }

    def _consolidate_memories(self):
        """Consolidate memories for all agents."""
        try:
            self.coder_agent.consolidate_memory()
            self.reviewer_agent.consolidate_memory()
            self.manager_agent.consolidate_memory()
        except Exception as e:
            self.system_logger.log_system_error(f"Memory consolidation failed: {str(e)}", e)

    def _format_workflow_result(self, state: WorkflowState) -> Dict[str, Any]:
        """Format the final workflow result."""
        return {
            "workflow_id": state.workflow_id,
            "status": state.status,
            "user_request": state.user_request,
            "folder_path": state.folder_path,
            "completed_steps": state.completed_steps,
            "errors": state.errors,
            "results": state.results,
            "agent_outputs": state.agent_outputs,
            "final_result": state.final_result,
            "summary": {
                "total_steps": len(state.completed_steps),
                "error_count": len(state.errors),
                "success": state.status == "completed",
                "has_code": "generated_code" in state.agent_outputs,
                "has_review": "code_review" in state.agent_outputs,
                "overall_quality": state.agent_outputs.get("code_review", {}).get("overall_score", 0)
            }
        }
