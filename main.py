"""Main entry point for the multi-agent system."""

import os
import sys
import argparse
from pathlib import Path

# Add the current directory to the Python path
sys.path.insert(0, str(Path(__file__).parent))

from config import Config
from workflow.agent_workflow import MultiAgentWorkflow
from utils.logger import System<PERSON>ogger

def setup_environment():
    """Set up the environment and ensure all dependencies are available."""
    # Ensure memory folder exists
    Config.ensure_memory_folder()
    
    # Initialize system logger
    logger = SystemLogger()
    logger.log_workflow_start("system", "Multi-agent system starting up")
    
    return logger

def run_cli_mode(user_request: str, folder_path: str = None):
    """Run the system in CLI mode."""
    logger = setup_environment()
    
    print("🤖 Multi-Agent Code Generation System")
    print("=" * 50)
    print(f"Request: {user_request}")
    if folder_path:
        print(f"Folder: {folder_path}")
    print("=" * 50)
    
    try:
        # Create and execute workflow
        workflow = MultiAgentWorkflow()
        result = workflow.execute_workflow(user_request, folder_path)
        
        # Display results
        print("\n📊 WORKFLOW RESULTS")
        print("=" * 50)
        print(f"Status: {result['status'].upper()}")
        print(f"Steps Completed: {result['summary']['total_steps']}")
        print(f"Errors: {result['summary']['error_count']}")
        
        if result['summary']['has_review']:
            print(f"Quality Score: {result['summary']['overall_quality']:.1f}/10")
        
        if result.get('final_result'):
            print("\n🎯 FINAL RESULT")
            print("=" * 50)
            print(result['final_result'])
        
        if result.get('errors'):
            print("\n⚠️ ERRORS")
            print("=" * 50)
            for error in result['errors']:
                print(f"- {error}")
        
        logger.log_workflow_end("cli_workflow", result['status'], "CLI execution completed")
        
    except Exception as e:
        print(f"\n❌ Error: {str(e)}")
        logger.log_system_error(f"CLI execution failed: {str(e)}", e)

def run_gui_mode():
    """Run the system in GUI mode using Streamlit."""
    logger = setup_environment()
    
    try:
        import streamlit.web.cli as stcli
        import sys
        
        # Set up Streamlit arguments
        sys.argv = [
            "streamlit",
            "run",
            "gui/streamlit_app.py",
            "--server.port=8501",
            "--server.address=localhost",
            "--browser.gatherUsageStats=false"
        ]
        
        print("🚀 Starting Streamlit GUI...")
        print("📱 Open your browser to: http://localhost:8501")
        
        logger.log_workflow_start("gui", "Starting Streamlit GUI")
        
        # Run Streamlit
        stcli.main()
        
    except ImportError:
        print("❌ Streamlit not installed. Please install it with: pip install streamlit")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error starting GUI: {str(e)}")
        logger.log_system_error(f"GUI startup failed: {str(e)}", e)
        sys.exit(1)

def check_vllm_servers():
    """Check if VLLM servers are accessible."""
    import requests
    
    servers = [
        ("Coder", Config.CODER_MODEL_URL),
        ("Reviewer", Config.REVIEWER_MODEL_URL),
        ("Manager", Config.MANAGER_MODEL_URL)
    ]
    
    print("🔍 Checking VLLM server connectivity...")
    
    all_accessible = True
    for name, url in servers:
        try:
            # Try to access the models endpoint
            response = requests.get(f"{url}/models", timeout=5)
            if response.status_code == 200:
                print(f"✅ {name} server accessible at {url}")
            else:
                print(f"⚠️ {name} server responded with status {response.status_code} at {url}")
                all_accessible = False
        except requests.exceptions.RequestException as e:
            print(f"❌ {name} server not accessible at {url}: {str(e)}")
            all_accessible = False
    
    if not all_accessible:
        print("\n⚠️ Warning: Some VLLM servers are not accessible.")
        print("Please ensure your VLLM servers are running on the configured ports.")
        print("You can update the configuration in the .env file.")
        
        response = input("\nContinue anyway? (y/N): ")
        if response.lower() != 'y':
            sys.exit(1)
    
    return all_accessible

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Multi-Agent Code Generation System")
    parser.add_argument(
        "--mode", 
        choices=["gui", "cli"], 
        default="gui",
        help="Run mode: gui (Streamlit interface) or cli (command line)"
    )
    parser.add_argument(
        "--request", 
        type=str,
        help="User request for CLI mode"
    )
    parser.add_argument(
        "--folder", 
        type=str,
        help="Project folder path for analysis"
    )
    parser.add_argument(
        "--skip-server-check",
        action="store_true",
        help="Skip VLLM server connectivity check"
    )
    
    args = parser.parse_args()
    
    # Display configuration
    print("🤖 Multi-Agent Code Generation System")
    print("=" * 50)
    print("Configuration:")
    print(f"  Coder Model: {Config.CODER_MODEL_URL}")
    print(f"  Reviewer Model: {Config.REVIEWER_MODEL_URL}")
    print(f"  Manager Model: {Config.MANAGER_MODEL_URL}")
    print(f"  Memory Folder: {Config.MEMORY_FOLDER}")
    print(f"  Max Tokens per Agent: {Config.MAX_TOKENS_PER_AGENT}")
    print("=" * 50)
    
    # Check server connectivity
    if not args.skip_server_check:
        check_vllm_servers()
    
    # Run in appropriate mode
    if args.mode == "cli":
        if not args.request:
            print("❌ Error: --request is required for CLI mode")
            parser.print_help()
            sys.exit(1)
        
        run_cli_mode(args.request, args.folder)
    
    else:  # GUI mode
        run_gui_mode()

if __name__ == "__main__":
    main()
