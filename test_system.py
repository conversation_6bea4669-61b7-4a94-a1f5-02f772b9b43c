#!/usr/bin/env python3
"""Test script for the multi-agent system."""

import sys
import os
from pathlib import Path

# Add the current directory to the Python path
sys.path.insert(0, str(Path(__file__).parent))

def test_imports():
    """Test that all modules can be imported."""
    print("🧪 Testing imports...")
    
    try:
        from config import Config
        print("✅ Config imported successfully")
        
        from utils.token_manager import TokenManager
        print("✅ TokenManager imported successfully")
        
        from utils.logger import AgentLogger, SystemLogger
        print("✅ Loggers imported successfully")
        
        from memory.memory_manager import AgentMemory
        print("✅ AgentMemory imported successfully")
        
        from tools.file_tools import FileAnalysisTool
        print("✅ FileAnalysisTool imported successfully")
        
        from agents.base_agent import BaseAgent
        print("✅ BaseAgent imported successfully")
        
        from agents.coder_agent import CoderAgent
        print("✅ CoderAgent imported successfully")
        
        from agents.reviewer_agent import ReviewerAgent
        print("✅ ReviewerAgent imported successfully")
        
        from agents.manager_agent import ManagerAgent
        print("✅ ManagerAgent imported successfully")
        
        from workflow.agent_workflow import MultiAgentWorkflow
        print("✅ MultiAgentWorkflow imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_configuration():
    """Test configuration loading."""
    print("\n🔧 Testing configuration...")
    
    try:
        from config import Config
        
        print(f"✅ Coder Model URL: {Config.CODER_MODEL_URL}")
        print(f"✅ Reviewer Model URL: {Config.REVIEWER_MODEL_URL}")
        print(f"✅ Manager Model URL: {Config.MANAGER_MODEL_URL}")
        print(f"✅ Temperature: {Config.TEMPERATURE}")
        print(f"✅ Max Tokens: {Config.MAX_TOKENS}")
        print(f"✅ Memory Folder: {Config.MEMORY_FOLDER}")
        
        # Test memory folder creation
        Config.ensure_memory_folder()
        if os.path.exists(Config.MEMORY_FOLDER):
            print(f"✅ Memory folder created: {Config.MEMORY_FOLDER}")
        else:
            print(f"❌ Failed to create memory folder: {Config.MEMORY_FOLDER}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False

def test_token_manager():
    """Test token management functionality."""
    print("\n🔢 Testing token manager...")
    
    try:
        from utils.token_manager import TokenManager
        
        token_manager = TokenManager()
        
        # Test token counting
        test_text = "Hello, world! This is a test."
        token_count = token_manager.count_tokens(test_text)
        print(f"✅ Token count for '{test_text}': {token_count}")
        
        # Test message token counting
        messages = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Hello!"},
            {"role": "assistant", "content": "Hi there! How can I help you?"}
        ]
        message_tokens = token_manager.count_message_tokens(messages)
        print(f"✅ Message tokens: {message_tokens}")
        
        # Test token limit checking
        is_within_limit = token_manager.is_within_limit(test_text, 100)
        print(f"✅ Within limit check: {is_within_limit}")
        
        return True
        
    except Exception as e:
        print(f"❌ Token manager error: {e}")
        return False

def test_memory_system():
    """Test memory management system."""
    print("\n🧠 Testing memory system...")
    
    try:
        from memory.memory_manager import AgentMemory
        
        # Create test memory
        memory = AgentMemory("test_agent")
        
        # Test short-term memory
        memory.add_short_term_memory("Test short-term memory", "test")
        print("✅ Short-term memory added")
        
        # Test long-term memory
        memory.add_long_term_memory("Test long-term memory", "important")
        print("✅ Long-term memory added")
        
        # Test conversation history
        memory.add_conversation_turn("user", "Hello")
        memory.add_conversation_turn("assistant", "Hi there!")
        print("✅ Conversation turns added")
        
        # Test memory retrieval
        recent_memories = memory.get_recent_memories(2)
        print(f"✅ Recent memories retrieved: {len(recent_memories)}")
        
        # Test memory summary
        summary = memory.get_memory_summary()
        print(f"✅ Memory summary: {summary}")
        
        return True
        
    except Exception as e:
        print(f"❌ Memory system error: {e}")
        return False

def test_file_tools():
    """Test file analysis tools."""
    print("\n📁 Testing file tools...")
    
    try:
        from tools.file_tools import FileAnalysisTool
        
        tool = FileAnalysisTool()
        
        # Test with current directory
        current_dir = os.getcwd()
        result = tool._run(current_dir, max_depth=2, include_content=False)
        
        if "error" in result:
            print(f"⚠️ File analysis returned error: {result['error']}")
        else:
            print(f"✅ File analysis completed")
            print(f"   - Project type: {result.get('summary', {}).get('project_type', 'Unknown')}")
            print(f"   - Total files: {result.get('summary', {}).get('total_files', 0)}")
            print(f"   - Programming languages: {result.get('summary', {}).get('programming_languages', [])}")
        
        return True
        
    except Exception as e:
        print(f"❌ File tools error: {e}")
        return False

def test_agents():
    """Test agent initialization (without LLM calls)."""
    print("\n🤖 Testing agent initialization...")
    
    try:
        # Note: This will fail if VLLM servers are not running, but we can test initialization
        from agents.coder_agent import CoderAgent
        from agents.reviewer_agent import ReviewerAgent
        from agents.manager_agent import ManagerAgent
        
        print("✅ Agent classes imported successfully")
        
        # Test agent descriptions
        coder = CoderAgent()
        reviewer = ReviewerAgent()
        manager = ManagerAgent()
        
        print(f"✅ Coder agent description: {coder.get_agent_description()[:50]}...")
        print(f"✅ Reviewer agent description: {reviewer.get_agent_description()[:50]}...")
        print(f"✅ Manager agent description: {manager.get_agent_description()[:50]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent initialization error: {e}")
        print("   Note: This is expected if VLLM servers are not running")
        return False

def main():
    """Run all tests."""
    print("🚀 Multi-Agent System Test Suite")
    print("=" * 50)
    
    tests = [
        ("Imports", test_imports),
        ("Configuration", test_configuration),
        ("Token Manager", test_token_manager),
        ("Memory System", test_memory_system),
        ("File Tools", test_file_tools),
        ("Agents", test_agents),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nTotal: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The system is ready to use.")
        return 0
    else:
        print("⚠️ Some tests failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
