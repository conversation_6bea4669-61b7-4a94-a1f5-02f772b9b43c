2025-05-30 10:56:03,387 - agent_reviewer - INFO - [INIT] Agent reviewer initialized with 0 tools
2025-05-30 14:42:40,069 - agent_reviewer - ERROR - Error: Failed to initialize LLM with full config: 1 validation error for VLLMOpenAI
__root__
  Client.__init__() got an unexpected keyword argument 'proxies' (type=type_error)
2025-05-30 14:42:40,070 - agent_reviewer - ERROR - Error: Failed to initialize LLM with minimal config: 1 validation error for VLLMOpenAI
__root__
  Client.__init__() got an unexpected keyword argument 'proxies' (type=type_error)
2025-05-30 14:42:40,070 - agent_reviewer - INFO - [INIT] Agent reviewer initialized with 0 tools
2025-05-30 14:42:40,072 - agent_reviewer - INFO - [MEMORY_UPDATE] conversation - add: user: Hello, this is a test message.......
2025-05-30 14:42:40,073 - agent_reviewer - INFO - [MEMORY_UPDATE] conversation - add: assistant: LLM not available for reviewer agent. Please ensur......
2025-05-30 14:42:40,073 - agent_reviewer - INFO - [MEMORY_UPDATE] short_term - add: User: Hello, this is a test message.
Assistant: LLM not available for reviewer agent. Please ensure ...
2025-05-30 14:42:40,073 - agent_reviewer - INFO - [RESPONSE] LLM not available for reviewer agent. Please ensure VLLM servers are running and properly configured.
2025-05-30 14:44:27,314 - agent_reviewer - INFO - [INIT] Agent reviewer initialized with 0 tools
2025-05-30 14:44:27,460 - agent_reviewer - INFO - [WORKFLOW_STEP] code_review: started
2025-05-30 14:44:27,460 - agent_reviewer - INFO - [MEMORY_UPDATE] conversation - add: user: 
Please conduct a comprehensive code review of the......
2025-05-30 14:44:27,491 - agent_reviewer - ERROR - Error: Error in direct processing: Error code: 400 - {'object': 'error', 'message': "This model's maximum context length is 4096 tokens. However, you requested 5171 tokens (1075 in the messages, 4096 in the completion). Please reduce the length of the messages or completion.", 'type': 'BadRequestError', 'param': None, 'code': 400}
2025-05-30 14:44:27,491 - agent_reviewer - ERROR - Error code: 400 - {'object': 'error', 'message': "This model's maximum context length is 4096 tokens. However, you requested 5171 tokens (1075 in the messages, 4096 in the completion). Please reduce the length of the messages or completion.", 'type': 'BadRequestError', 'param': None, 'code': 400}
Traceback (most recent call last):
  File "/home/<USER>/Documents/n8n/multi-agent-workflow/agents/base_agent.py", line 208, in _process_direct
    response = self.llm.invoke(full_prompt)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/language_models/llms.py", line 390, in invoke
    self.generate_prompt(
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/language_models/llms.py", line 755, in generate_prompt
    return self.generate(prompt_strings, stop=stop, callbacks=callbacks, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/language_models/llms.py", line 950, in generate
    output = self._generate_helper(
             ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/language_models/llms.py", line 792, in _generate_helper
    raise e
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/language_models/llms.py", line 779, in _generate_helper
    self._generate(
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_community/llms/openai.py", line 463, in _generate
    response = completion_with_retry(
               ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_community/llms/openai.py", line 121, in completion_with_retry
    return llm.client.create(**kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/openai/_utils/_utils.py", line 275, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/openai/resources/completions.py", line 539, in create
    return self._post(
           ^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/openai/_base_client.py", line 1280, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/openai/_base_client.py", line 957, in request
    return self._request(
           ^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/openai/_base_client.py", line 1061, in _request
    raise self._make_status_error_from_response(err.response) from None
openai.BadRequestError: Error code: 400 - {'object': 'error', 'message': "This model's maximum context length is 4096 tokens. However, you requested 5171 tokens (1075 in the messages, 4096 in the completion). Please reduce the length of the messages or completion.", 'type': 'BadRequestError', 'param': None, 'code': 400}
2025-05-30 14:44:27,492 - agent_reviewer - INFO - [MEMORY_UPDATE] conversation - add: assistant: I'm sorry, I encountered an error while processing......
2025-05-30 14:44:27,492 - agent_reviewer - INFO - [MEMORY_UPDATE] short_term - add: User: 
Please conduct a comprehensive code review of the following implementation:

CODE TO REVIEW:
...
2025-05-30 14:44:27,492 - agent_reviewer - INFO - [RESPONSE] I'm sorry, I encountered an error while processing your request: Error code: 400 - {'object': 'error', 'message': "This model's maximum context length is 4096 tokens. However, you requested 5171 token...
2025-05-30 14:44:27,493 - agent_reviewer - INFO - [MEMORY_UPDATE] long_term - add: Code review completed - Overall score: N/A...
2025-05-30 14:44:27,493 - agent_reviewer - INFO - [WORKFLOW_STEP] code_review: completed
2025-05-30 14:44:27,493 - agent_reviewer - INFO - [WORKFLOW_STEP] requirements_validation: started
2025-05-30 14:44:27,493 - agent_reviewer - INFO - [MEMORY_UPDATE] conversation - add: user: 
Please validate whether the following code implem......
2025-05-30 14:44:27,506 - agent_reviewer - ERROR - Error: Error in direct processing: Error code: 400 - {'object': 'error', 'message': "This model's maximum context length is 4096 tokens. However, you requested 5628 tokens (1532 in the messages, 4096 in the completion). Please reduce the length of the messages or completion.", 'type': 'BadRequestError', 'param': None, 'code': 400}
2025-05-30 14:44:27,506 - agent_reviewer - ERROR - Error code: 400 - {'object': 'error', 'message': "This model's maximum context length is 4096 tokens. However, you requested 5628 tokens (1532 in the messages, 4096 in the completion). Please reduce the length of the messages or completion.", 'type': 'BadRequestError', 'param': None, 'code': 400}
Traceback (most recent call last):
  File "/home/<USER>/Documents/n8n/multi-agent-workflow/agents/base_agent.py", line 208, in _process_direct
    response = self.llm.invoke(full_prompt)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/language_models/llms.py", line 390, in invoke
    self.generate_prompt(
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/language_models/llms.py", line 755, in generate_prompt
    return self.generate(prompt_strings, stop=stop, callbacks=callbacks, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/language_models/llms.py", line 950, in generate
    output = self._generate_helper(
             ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/language_models/llms.py", line 792, in _generate_helper
    raise e
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/language_models/llms.py", line 779, in _generate_helper
    self._generate(
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_community/llms/openai.py", line 463, in _generate
    response = completion_with_retry(
               ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_community/llms/openai.py", line 121, in completion_with_retry
    return llm.client.create(**kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/openai/_utils/_utils.py", line 275, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/openai/resources/completions.py", line 539, in create
    return self._post(
           ^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/openai/_base_client.py", line 1280, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/openai/_base_client.py", line 957, in request
    return self._request(
           ^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/openai/_base_client.py", line 1061, in _request
    raise self._make_status_error_from_response(err.response) from None
openai.BadRequestError: Error code: 400 - {'object': 'error', 'message': "This model's maximum context length is 4096 tokens. However, you requested 5628 tokens (1532 in the messages, 4096 in the completion). Please reduce the length of the messages or completion.", 'type': 'BadRequestError', 'param': None, 'code': 400}
2025-05-30 14:44:27,507 - agent_reviewer - INFO - [MEMORY_UPDATE] conversation - add: assistant: I'm sorry, I encountered an error while processing......
2025-05-30 14:44:27,507 - agent_reviewer - INFO - [MEMORY_UPDATE] short_term - add: User: 
Please validate whether the following code implementation meets the specified requirements:

...
2025-05-30 14:44:27,508 - agent_reviewer - INFO - [RESPONSE] I'm sorry, I encountered an error while processing your request: Error code: 400 - {'object': 'error', 'message': "This model's maximum context length is 4096 tokens. However, you requested 5628 token...
2025-05-30 14:44:27,508 - agent_reviewer - INFO - [MEMORY_UPDATE] long_term - add: Requirements validation - Compliance: N/A%...
2025-05-30 14:44:27,508 - agent_reviewer - INFO - [WORKFLOW_STEP] requirements_validation: completed
2025-05-30 14:44:27,550 - agent_reviewer - INFO - [MEMORY_UPDATE] long_term - add: User: Hello, this is a test message.
Assistant: LLM not available for reviewer agent. Please ensure ...
2025-05-30 14:44:27,551 - agent_reviewer - INFO - [MEMORY_UPDATE] long_term - add: User: 
Please conduct a comprehensive code review of the following implementation:

CODE TO REVIEW:
...
2025-05-30 14:44:27,551 - agent_reviewer - INFO - [MEMORY_UPDATE] long_term - add: User: 
Please validate whether the following code implementation meets the specified requirements:

...
2025-05-30 14:44:27,552 - agent_reviewer - INFO - [MEMORY_UPDATE] consolidation - completed: Moved 3 memories...
2025-05-30 14:44:27,552 - agent_reviewer - INFO - [MEMORY] Memory consolidation completed
