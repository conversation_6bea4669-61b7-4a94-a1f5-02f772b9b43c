2025-05-30 10:56:03,357 - agent_coder - INFO - [INIT] Agent coder initialized with 1 tools
2025-05-30 14:42:40,068 - agent_coder - ERROR - Error: Failed to initialize LLM with full config: 1 validation error for VLLMOpenAI
__root__
  Client.__init__() got an unexpected keyword argument 'proxies' (type=type_error)
2025-05-30 14:42:40,068 - agent_coder - ERROR - Error: Failed to initialize LLM with minimal config: 1 validation error for VLLMOpenAI
__root__
  Client.__init__() got an unexpected keyword argument 'proxies' (type=type_error)
2025-05-30 14:42:40,068 - agent_coder - INFO - [INIT] Agent coder initialized with 1 tools
2025-05-30 14:42:40,071 - agent_coder - INFO - [MEMORY_UPDATE] conversation - add: user: Hello, this is a test message.......
2025-05-30 14:42:40,072 - agent_coder - INFO - [MEMORY_UPDATE] conversation - add: assistant: <PERSON><PERSON> not available for coder agent. Please ensure V......
2025-05-30 14:42:40,072 - agent_coder - INFO - [MEMORY_UPDATE] short_term - add: User: Hello, this is a test message.
Assistant: LLM not available for coder agent. Please ensure VLL...
2025-05-30 14:42:40,072 - agent_coder - INFO - [RESPONSE] LLM not available for coder agent. Please ensure VLLM servers are running and properly configured.
2025-05-30 14:44:27,282 - agent_coder - INFO - [INIT] Agent coder initialized with 1 tools
2025-05-30 14:44:27,374 - agent_coder - INFO - [WORKFLOW_STEP] analyze_project: started
2025-05-30 14:44:27,375 - agent_coder - INFO - [MEMORY_UPDATE] long_term - add: Project analysis for /home/<USER>/Documents/n8n/multi-agent-workflow/test-content: {'total_fil...
2025-05-30 14:44:27,375 - agent_coder - INFO - [WORKFLOW_STEP] analyze_project: completed
2025-05-30 14:44:27,376 - agent_coder - INFO - [WORKFLOW_STEP] generate_plan: started
2025-05-30 14:44:27,376 - agent_coder - INFO - [MEMORY_UPDATE] conversation - add: user: 
Based on the following requirements and existing ......
2025-05-30 14:44:27,410 - agent_coder - ERROR - Error: Error in tool processing: Error code: 400 - {'object': 'error', 'message': "This model's maximum context length is 4096 tokens. However, you requested 4646 tokens (550 in the messages, 4096 in the completion). Please reduce the length of the messages or completion.", 'type': 'BadRequestError', 'param': None, 'code': 400}
2025-05-30 14:44:27,410 - agent_coder - ERROR - Error code: 400 - {'object': 'error', 'message': "This model's maximum context length is 4096 tokens. However, you requested 4646 tokens (550 in the messages, 4096 in the completion). Please reduce the length of the messages or completion.", 'type': 'BadRequestError', 'param': None, 'code': 400}
Traceback (most recent call last):
  File "/home/<USER>/Documents/n8n/multi-agent-workflow/agents/base_agent.py", line 178, in _process_with_tools
    result = self.agent_executor.invoke(agent_input)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain/chains/base.py", line 170, in invoke
    raise e
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain/chains/base.py", line 160, in invoke
    self._call(inputs, run_manager=run_manager)
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain/agents/agent.py", line 1624, in _call
    next_step_output = self._take_next_step(
                       ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain/agents/agent.py", line 1330, in _take_next_step
    [
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain/agents/agent.py", line 1330, in <listcomp>
    [
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain/agents/agent.py", line 1358, in _iter_next_step
    output = self._action_agent.plan(
             ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain/agents/agent.py", line 465, in plan
    for chunk in self.runnable.stream(inputs, config={"callbacks": callbacks}):
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/runnables/base.py", line 3407, in stream
    yield from self.transform(iter([input]), config, **kwargs)
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/runnables/base.py", line 3394, in transform
    yield from self._transform_stream_with_config(
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/runnables/base.py", line 2197, in _transform_stream_with_config
    chunk: Output = context.run(next, iterator)  # type: ignore
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/runnables/base.py", line 3357, in _transform
    yield from final_pipeline
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/runnables/base.py", line 1413, in transform
    for ichunk in input:
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/runnables/base.py", line 5561, in transform
    yield from self.bound.transform(
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/runnables/base.py", line 1431, in transform
    yield from self.stream(final, config, **kwargs)
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/language_models/llms.py", line 576, in stream
    raise e
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/language_models/llms.py", line 560, in stream
    for chunk in self._stream(
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_community/llms/openai.py", line 365, in _stream
    for stream_resp in completion_with_retry(
                       ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_community/llms/openai.py", line 121, in completion_with_retry
    return llm.client.create(**kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/openai/_utils/_utils.py", line 275, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/openai/resources/completions.py", line 539, in create
    return self._post(
           ^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/openai/_base_client.py", line 1280, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/openai/_base_client.py", line 957, in request
    return self._request(
           ^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/openai/_base_client.py", line 1061, in _request
    raise self._make_status_error_from_response(err.response) from None
openai.BadRequestError: Error code: 400 - {'object': 'error', 'message': "This model's maximum context length is 4096 tokens. However, you requested 4646 tokens (550 in the messages, 4096 in the completion). Please reduce the length of the messages or completion.", 'type': 'BadRequestError', 'param': None, 'code': 400}
2025-05-30 14:44:27,422 - agent_coder - ERROR - Error: Error in direct processing: Error code: 400 - {'object': 'error', 'message': "This model's maximum context length is 4096 tokens. However, you requested 4632 tokens (536 in the messages, 4096 in the completion). Please reduce the length of the messages or completion.", 'type': 'BadRequestError', 'param': None, 'code': 400}
2025-05-30 14:44:27,422 - agent_coder - ERROR - Error code: 400 - {'object': 'error', 'message': "This model's maximum context length is 4096 tokens. However, you requested 4632 tokens (536 in the messages, 4096 in the completion). Please reduce the length of the messages or completion.", 'type': 'BadRequestError', 'param': None, 'code': 400}
Traceback (most recent call last):
  File "/home/<USER>/Documents/n8n/multi-agent-workflow/agents/base_agent.py", line 208, in _process_direct
    response = self.llm.invoke(full_prompt)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/language_models/llms.py", line 390, in invoke
    self.generate_prompt(
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/language_models/llms.py", line 755, in generate_prompt
    return self.generate(prompt_strings, stop=stop, callbacks=callbacks, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/language_models/llms.py", line 950, in generate
    output = self._generate_helper(
             ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/language_models/llms.py", line 792, in _generate_helper
    raise e
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/language_models/llms.py", line 779, in _generate_helper
    self._generate(
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_community/llms/openai.py", line 463, in _generate
    response = completion_with_retry(
               ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_community/llms/openai.py", line 121, in completion_with_retry
    return llm.client.create(**kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/openai/_utils/_utils.py", line 275, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/openai/resources/completions.py", line 539, in create
    return self._post(
           ^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/openai/_base_client.py", line 1280, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/openai/_base_client.py", line 957, in request
    return self._request(
           ^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/openai/_base_client.py", line 1061, in _request
    raise self._make_status_error_from_response(err.response) from None
openai.BadRequestError: Error code: 400 - {'object': 'error', 'message': "This model's maximum context length is 4096 tokens. However, you requested 4632 tokens (536 in the messages, 4096 in the completion). Please reduce the length of the messages or completion.", 'type': 'BadRequestError', 'param': None, 'code': 400}
2025-05-30 14:44:27,422 - agent_coder - INFO - [MEMORY_UPDATE] conversation - add: assistant: I'm sorry, I encountered an error while processing......
2025-05-30 14:44:27,423 - agent_coder - INFO - [MEMORY_UPDATE] short_term - add: User: 
Based on the following requirements and existing project context, create a comprehensive proj...
2025-05-30 14:44:27,424 - agent_coder - INFO - [RESPONSE] I'm sorry, I encountered an error while processing your request: Error code: 400 - {'object': 'error', 'message': "This model's maximum context length is 4096 tokens. However, you requested 4632 token...
2025-05-30 14:44:27,424 - agent_coder - INFO - [MEMORY_UPDATE] long_term - add: Project plan: I'm sorry, I encountered an error while processing your request: Error code: 400 - {'o...
2025-05-30 14:44:27,424 - agent_coder - INFO - [WORKFLOW_STEP] generate_plan: completed
2025-05-30 14:44:27,424 - agent_coder - INFO - [WORKFLOW_STEP] generate_code: started
2025-05-30 14:44:27,424 - agent_coder - INFO - [MEMORY_UPDATE] conversation - add: user: 
Generate high-quality code based on the following......
2025-05-30 14:44:27,443 - agent_coder - ERROR - Error: Error in tool processing: Error code: 400 - {'object': 'error', 'message': "This model's maximum context length is 4096 tokens. However, you requested 5434 tokens (1338 in the messages, 4096 in the completion). Please reduce the length of the messages or completion.", 'type': 'BadRequestError', 'param': None, 'code': 400}
2025-05-30 14:44:27,443 - agent_coder - ERROR - Error code: 400 - {'object': 'error', 'message': "This model's maximum context length is 4096 tokens. However, you requested 5434 tokens (1338 in the messages, 4096 in the completion). Please reduce the length of the messages or completion.", 'type': 'BadRequestError', 'param': None, 'code': 400}
Traceback (most recent call last):
  File "/home/<USER>/Documents/n8n/multi-agent-workflow/agents/base_agent.py", line 178, in _process_with_tools
    result = self.agent_executor.invoke(agent_input)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain/chains/base.py", line 170, in invoke
    raise e
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain/chains/base.py", line 160, in invoke
    self._call(inputs, run_manager=run_manager)
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain/agents/agent.py", line 1624, in _call
    next_step_output = self._take_next_step(
                       ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain/agents/agent.py", line 1330, in _take_next_step
    [
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain/agents/agent.py", line 1330, in <listcomp>
    [
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain/agents/agent.py", line 1358, in _iter_next_step
    output = self._action_agent.plan(
             ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain/agents/agent.py", line 465, in plan
    for chunk in self.runnable.stream(inputs, config={"callbacks": callbacks}):
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/runnables/base.py", line 3407, in stream
    yield from self.transform(iter([input]), config, **kwargs)
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/runnables/base.py", line 3394, in transform
    yield from self._transform_stream_with_config(
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/runnables/base.py", line 2197, in _transform_stream_with_config
    chunk: Output = context.run(next, iterator)  # type: ignore
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/runnables/base.py", line 3357, in _transform
    yield from final_pipeline
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/runnables/base.py", line 1413, in transform
    for ichunk in input:
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/runnables/base.py", line 5561, in transform
    yield from self.bound.transform(
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/runnables/base.py", line 1431, in transform
    yield from self.stream(final, config, **kwargs)
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/language_models/llms.py", line 576, in stream
    raise e
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/language_models/llms.py", line 560, in stream
    for chunk in self._stream(
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_community/llms/openai.py", line 365, in _stream
    for stream_resp in completion_with_retry(
                       ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_community/llms/openai.py", line 121, in completion_with_retry
    return llm.client.create(**kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/openai/_utils/_utils.py", line 275, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/openai/resources/completions.py", line 539, in create
    return self._post(
           ^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/openai/_base_client.py", line 1280, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/openai/_base_client.py", line 957, in request
    return self._request(
           ^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/openai/_base_client.py", line 1061, in _request
    raise self._make_status_error_from_response(err.response) from None
openai.BadRequestError: Error code: 400 - {'object': 'error', 'message': "This model's maximum context length is 4096 tokens. However, you requested 5434 tokens (1338 in the messages, 4096 in the completion). Please reduce the length of the messages or completion.", 'type': 'BadRequestError', 'param': None, 'code': 400}
2025-05-30 14:44:27,457 - agent_coder - ERROR - Error: Error in direct processing: Error code: 400 - {'object': 'error', 'message': "This model's maximum context length is 4096 tokens. However, you requested 5420 tokens (1324 in the messages, 4096 in the completion). Please reduce the length of the messages or completion.", 'type': 'BadRequestError', 'param': None, 'code': 400}
2025-05-30 14:44:27,457 - agent_coder - ERROR - Error code: 400 - {'object': 'error', 'message': "This model's maximum context length is 4096 tokens. However, you requested 5420 tokens (1324 in the messages, 4096 in the completion). Please reduce the length of the messages or completion.", 'type': 'BadRequestError', 'param': None, 'code': 400}
Traceback (most recent call last):
  File "/home/<USER>/Documents/n8n/multi-agent-workflow/agents/base_agent.py", line 208, in _process_direct
    response = self.llm.invoke(full_prompt)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/language_models/llms.py", line 390, in invoke
    self.generate_prompt(
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/language_models/llms.py", line 755, in generate_prompt
    return self.generate(prompt_strings, stop=stop, callbacks=callbacks, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/language_models/llms.py", line 950, in generate
    output = self._generate_helper(
             ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/language_models/llms.py", line 792, in _generate_helper
    raise e
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/language_models/llms.py", line 779, in _generate_helper
    self._generate(
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_community/llms/openai.py", line 463, in _generate
    response = completion_with_retry(
               ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_community/llms/openai.py", line 121, in completion_with_retry
    return llm.client.create(**kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/openai/_utils/_utils.py", line 275, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/openai/resources/completions.py", line 539, in create
    return self._post(
           ^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/openai/_base_client.py", line 1280, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/openai/_base_client.py", line 957, in request
    return self._request(
           ^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/openai/_base_client.py", line 1061, in _request
    raise self._make_status_error_from_response(err.response) from None
openai.BadRequestError: Error code: 400 - {'object': 'error', 'message': "This model's maximum context length is 4096 tokens. However, you requested 5420 tokens (1324 in the messages, 4096 in the completion). Please reduce the length of the messages or completion.", 'type': 'BadRequestError', 'param': None, 'code': 400}
2025-05-30 14:44:27,458 - agent_coder - INFO - [MEMORY_UPDATE] conversation - add: assistant: I'm sorry, I encountered an error while processing......
2025-05-30 14:44:27,458 - agent_coder - INFO - [MEMORY_UPDATE] short_term - add: User: 
Generate high-quality code based on the following specification:

User Request: Add web inter...
2025-05-30 14:44:27,459 - agent_coder - INFO - [RESPONSE] I'm sorry, I encountered an error while processing your request: Error code: 400 - {'object': 'error', 'message': "This model's maximum context length is 4096 tokens. However, you requested 5420 token...
2025-05-30 14:44:27,459 - agent_coder - INFO - [MEMORY_UPDATE] long_term - add: Generated code for: User Request: Add web interface

Project Plan:
I'm sorry, I encountered an error...
2025-05-30 14:44:27,460 - agent_coder - INFO - [WORKFLOW_STEP] generate_code: completed
2025-05-30 14:44:27,547 - agent_coder - INFO - [MEMORY_UPDATE] long_term - add: User: Hello, this is a test message.
Assistant: LLM not available for coder agent. Please ensure VLL...
2025-05-30 14:44:27,547 - agent_coder - INFO - [MEMORY_UPDATE] long_term - add: User: 
Based on the following requirements and existing project context, create a comprehensive proj...
2025-05-30 14:44:27,549 - agent_coder - INFO - [MEMORY_UPDATE] long_term - add: User: 
Generate high-quality code based on the following specification:

User Request: Add web inter...
2025-05-30 14:44:27,550 - agent_coder - INFO - [MEMORY_UPDATE] consolidation - completed: Moved 3 memories...
2025-05-30 14:44:27,550 - agent_coder - INFO - [MEMORY] Memory consolidation completed
