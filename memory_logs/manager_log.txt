2025-05-30 10:56:03,416 - agent_manager - INFO - [INIT] Agent manager initialized with 0 tools
2025-05-30 14:42:40,070 - agent_manager - ERROR - Error: Failed to initialize LLM with full config: 1 validation error for VLLMOpenAI
__root__
  Client.__init__() got an unexpected keyword argument 'proxies' (type=type_error)
2025-05-30 14:42:40,071 - agent_manager - ERROR - Error: Failed to initialize LLM with minimal config: 1 validation error for VLLMOpenAI
__root__
  Client.__init__() got an unexpected keyword argument 'proxies' (type=type_error)
2025-05-30 14:42:40,071 - agent_manager - INFO - [INIT] Agent manager initialized with 0 tools
2025-05-30 14:42:40,073 - agent_manager - INFO - [MEMORY_UPDATE] conversation - add: user: Hello, this is a test message.......
2025-05-30 14:42:40,073 - agent_manager - INFO - [MEMORY_UPDATE] conversation - add: assistant: LLM not available for manager agent. Please ensure......
2025-05-30 14:42:40,073 - agent_manager - INFO - [MEMORY_UPDATE] short_term - add: User: Hello, this is a test message.
Assistant: LLM not available for manager agent. Please ensure V...
2025-05-30 14:42:40,074 - agent_manager - INFO - [RESPONSE] LLM not available for manager agent. Please ensure VLLM servers are running and properly configured.
2025-05-30 14:44:27,345 - agent_manager - INFO - [INIT] Agent manager initialized with 0 tools
2025-05-30 14:44:27,346 - agent_manager - INFO - [WORKFLOW_STEP] workflow_planning: started
2025-05-30 14:44:27,346 - agent_manager - INFO - [MEMORY_UPDATE] conversation - add: user: 
Analyze the following user request and create a d......
2025-05-30 14:44:27,368 - agent_manager - ERROR - Error: Error in direct processing: Error code: 400 - {'object': 'error', 'message': "This model's maximum context length is 4096 tokens. However, you requested 4729 tokens (633 in the messages, 4096 in the completion). Please reduce the length of the messages or completion.", 'type': 'BadRequestError', 'param': None, 'code': 400}
2025-05-30 14:44:27,369 - agent_manager - ERROR - Error code: 400 - {'object': 'error', 'message': "This model's maximum context length is 4096 tokens. However, you requested 4729 tokens (633 in the messages, 4096 in the completion). Please reduce the length of the messages or completion.", 'type': 'BadRequestError', 'param': None, 'code': 400}
Traceback (most recent call last):
  File "/home/<USER>/Documents/n8n/multi-agent-workflow/agents/base_agent.py", line 208, in _process_direct
    response = self.llm.invoke(full_prompt)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/language_models/llms.py", line 390, in invoke
    self.generate_prompt(
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/language_models/llms.py", line 755, in generate_prompt
    return self.generate(prompt_strings, stop=stop, callbacks=callbacks, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/language_models/llms.py", line 950, in generate
    output = self._generate_helper(
             ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/language_models/llms.py", line 792, in _generate_helper
    raise e
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/language_models/llms.py", line 779, in _generate_helper
    self._generate(
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_community/llms/openai.py", line 463, in _generate
    response = completion_with_retry(
               ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_community/llms/openai.py", line 121, in completion_with_retry
    return llm.client.create(**kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/openai/_utils/_utils.py", line 275, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/openai/resources/completions.py", line 539, in create
    return self._post(
           ^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/openai/_base_client.py", line 1280, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/openai/_base_client.py", line 957, in request
    return self._request(
           ^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/openai/_base_client.py", line 1061, in _request
    raise self._make_status_error_from_response(err.response) from None
openai.BadRequestError: Error code: 400 - {'object': 'error', 'message': "This model's maximum context length is 4096 tokens. However, you requested 4729 tokens (633 in the messages, 4096 in the completion). Please reduce the length of the messages or completion.", 'type': 'BadRequestError', 'param': None, 'code': 400}
2025-05-30 14:44:27,373 - agent_manager - INFO - [MEMORY_UPDATE] conversation - add: assistant: I'm sorry, I encountered an error while processing......
2025-05-30 14:44:27,373 - agent_manager - INFO - [MEMORY_UPDATE] short_term - add: User: 
Analyze the following user request and create a detailed workflow plan:

USER REQUEST:
Add we...
2025-05-30 14:44:27,373 - agent_manager - INFO - [RESPONSE] I'm sorry, I encountered an error while processing your request: Error code: 400 - {'object': 'error', 'message': "This model's maximum context length is 4096 tokens. However, you requested 4729 token...
2025-05-30 14:44:27,374 - agent_manager - INFO - [MEMORY_UPDATE] long_term - add: Workflow plan created for: Add web interface......
2025-05-30 14:44:27,374 - agent_manager - INFO - [WORKFLOW_STEP] workflow_planning: completed
2025-05-30 14:44:27,508 - agent_manager - INFO - [WORKFLOW_STEP] result_synthesis: started
2025-05-30 14:44:27,509 - agent_manager - INFO - [MEMORY_UPDATE] conversation - add: user: 
Synthesize a comprehensive final result based on ......
2025-05-30 14:44:27,544 - agent_manager - ERROR - Error: Error in direct processing: Error code: 400 - {'object': 'error', 'message': "This model's maximum context length is 4096 tokens. However, you requested 13819 tokens (9723 in the messages, 4096 in the completion). Please reduce the length of the messages or completion.", 'type': 'BadRequestError', 'param': None, 'code': 400}
2025-05-30 14:44:27,544 - agent_manager - ERROR - Error code: 400 - {'object': 'error', 'message': "This model's maximum context length is 4096 tokens. However, you requested 13819 tokens (9723 in the messages, 4096 in the completion). Please reduce the length of the messages or completion.", 'type': 'BadRequestError', 'param': None, 'code': 400}
Traceback (most recent call last):
  File "/home/<USER>/Documents/n8n/multi-agent-workflow/agents/base_agent.py", line 208, in _process_direct
    response = self.llm.invoke(full_prompt)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/language_models/llms.py", line 390, in invoke
    self.generate_prompt(
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/language_models/llms.py", line 755, in generate_prompt
    return self.generate(prompt_strings, stop=stop, callbacks=callbacks, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/language_models/llms.py", line 950, in generate
    output = self._generate_helper(
             ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/language_models/llms.py", line 792, in _generate_helper
    raise e
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_core/language_models/llms.py", line 779, in _generate_helper
    self._generate(
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_community/llms/openai.py", line 463, in _generate
    response = completion_with_retry(
               ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/langchain_community/llms/openai.py", line 121, in completion_with_retry
    return llm.client.create(**kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/openai/_utils/_utils.py", line 275, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/openai/resources/completions.py", line 539, in create
    return self._post(
           ^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/openai/_base_client.py", line 1280, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/openai/_base_client.py", line 957, in request
    return self._request(
           ^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.11/site-packages/openai/_base_client.py", line 1061, in _request
    raise self._make_status_error_from_response(err.response) from None
openai.BadRequestError: Error code: 400 - {'object': 'error', 'message': "This model's maximum context length is 4096 tokens. However, you requested 13819 tokens (9723 in the messages, 4096 in the completion). Please reduce the length of the messages or completion.", 'type': 'BadRequestError', 'param': None, 'code': 400}
2025-05-30 14:44:27,544 - agent_manager - INFO - [MEMORY_UPDATE] conversation - add: assistant: I'm sorry, I encountered an error while processing......
2025-05-30 14:44:27,545 - agent_manager - INFO - [MEMORY_UPDATE] short_term - add: User: 
Synthesize a comprehensive final result based on the following workflow outputs:

ORIGINAL US...
2025-05-30 14:44:27,546 - agent_manager - INFO - [RESPONSE] I'm sorry, I encountered an error while processing your request: Error code: 400 - {'object': 'error', 'message': "This model's maximum context length is 4096 tokens. However, you requested 13819 toke...
2025-05-30 14:44:27,546 - agent_manager - INFO - [MEMORY_UPDATE] long_term - add: Final result synthesized - Status: N/A...
2025-05-30 14:44:27,547 - agent_manager - INFO - [WORKFLOW_STEP] result_synthesis: completed
2025-05-30 14:44:27,552 - agent_manager - INFO - [MEMORY_UPDATE] long_term - add: User: Hello, this is a test message.
Assistant: LLM not available for manager agent. Please ensure V...
2025-05-30 14:44:27,553 - agent_manager - INFO - [MEMORY_UPDATE] long_term - add: User: 
Analyze the following user request and create a detailed workflow plan:

USER REQUEST:
Add we...
2025-05-30 14:44:27,555 - agent_manager - INFO - [MEMORY_UPDATE] long_term - add: User: 
Synthesize a comprehensive final result based on the following workflow outputs:

ORIGINAL US...
2025-05-30 14:44:27,556 - agent_manager - INFO - [MEMORY_UPDATE] consolidation - completed: Moved 3 memories...
2025-05-30 14:44:27,556 - agent_manager - INFO - [MEMORY] Memory consolidation completed
